#!/usr/bin/env python3
"""
测试ASTA兼容接口
演示如何使用ASTA兼容的命令行接口

Author: Augment Agent
Date: 2025-07-31
"""

import os
import sys
import subprocess
from pathlib import Path

def test_asta_compatible():
    """测试ASTA兼容接口"""
    
    # 测试文件路径
    test_file = r"E:\fix_data\align-compare\GY5_K053-1_No Filter_60S_Bin2_UTC20250628_193509_-14.9C_.fit"
    
    print("=" * 60)
    print("ASTA兼容接口测试")
    print("=" * 60)
    print(f"测试文件: {test_file}")
    
    # 检查测试文件是否存在
    if not os.path.exists(test_file):
        print(f"错误: 测试文件不存在: {test_file}")
        return False
    
    # 创建输出目录
    csv_output_dir = "noise_lines/asta_results/csv"
    image_output_dir = "noise_lines/asta_results/images"
    
    os.makedirs(csv_output_dir, exist_ok=True)
    os.makedirs(image_output_dir, exist_ok=True)
    
    print(f"CSV输出目录: {csv_output_dir}")
    print(f"图像输出目录: {image_output_dir}")
    print("-" * 60)
    
    # 测试用例列表
    test_cases = [
        {
            'name': '基本测试（无模型文件）',
            'model': 'nonexistent_model.h5',
            'threshold': 0.5,
            'save_options': ['--save']
        },
        {
            'name': '完整测试（无模型文件）',
            'model': 'nonexistent_model.h5', 
            'threshold': 0.75,
            'save_options': ['--save', '--save_mask', '--save_predicted_mask']
        },
        {
            'name': '严格阈值测试',
            'model': 'nonexistent_model.h5',
            'threshold': 0.3,
            'save_options': ['--save', '--save_mask']
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 40)
        
        # 构建命令
        cmd = [
            'python', 'asta_compatible.py',
            test_case['model'],
            test_file,
            '--unet_threshold', str(test_case['threshold']),
            '--csv_output_dir', csv_output_dir,
            '--image_output_dir', image_output_dir,
            '--time_processing'
        ]
        
        # 添加保存选项
        cmd.extend(test_case['save_options'])
        
        print(f"命令: {' '.join(cmd)}")
        
        try:
            # 执行命令
            result = subprocess.run(
                cmd,
                cwd=os.getcwd(),  # 使用当前工作目录
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            if result.returncode == 0:
                print("✓ 测试成功")
                print("输出:")
                print(result.stdout)
                success_count += 1
            else:
                print("✗ 测试失败")
                print("错误输出:")
                print(result.stderr)
                if result.stdout:
                    print("标准输出:")
                    print(result.stdout)
                    
        except subprocess.TimeoutExpired:
            print("✗ 测试超时")
        except Exception as e:
            print(f"✗ 测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"测试完成: {success_count}/{len(test_cases)} 个测试成功")
    
    # 检查输出文件
    print("\n检查输出文件:")
    csv_files = list(Path(csv_output_dir).glob("*.csv"))
    image_files = list(Path(image_output_dir).glob("*.png")) + list(Path(image_output_dir).glob("*.jpg"))
    
    print(f"CSV文件: {len(csv_files)} 个")
    for f in csv_files:
        print(f"  - {f}")
    
    print(f"图像文件: {len(image_files)} 个")
    for f in image_files:
        print(f"  - {f}")
    
    print("=" * 60)
    
    return success_count == len(test_cases)

def demonstrate_asta_usage():
    """演示ASTA兼容接口的使用方法"""
    
    print("=" * 60)
    print("ASTA兼容接口使用演示")
    print("=" * 60)
    
    print("1. 基本使用方法:")
    print("python asta_compatible.py model.h5 sample.fits")
    print()
    
    print("2. 指定U-Net阈值:")
    print("python asta_compatible.py model.h5 sample.fits --unet_threshold 0.75")
    print()
    
    print("3. 保存所有结果:")
    print("python asta_compatible.py model.h5 sample.fits \\")
    print("    --unet_threshold 0.75 \\")
    print("    --save \\")
    print("    --save_mask \\")
    print("    --save_predicted_mask \\")
    print("    --csv_output_dir results/csv \\")
    print("    --image_output_dir results/images")
    print()
    
    print("4. 显示处理时间:")
    print("python asta_compatible.py model.h5 sample.fits --time_processing")
    print()
    
    print("注意事项:")
    print("- 如果model.h5文件存在且安装了TensorFlow，将使用真实的ASTA模型")
    print("- 否则将使用模拟的ASTA方法（基于传统图像处理）")
    print("- 输出格式与原始ASTA工具兼容")
    print("- 支持所有原始ASTA工具的命令行参数")
    
    print("=" * 60)

def main():
    """主函数"""
    print("ASTA兼容接口测试和演示")
    
    # 演示使用方法
    demonstrate_asta_usage()
    
    # 运行测试
    print("\n开始运行测试...")
    success = test_asta_compatible()
    
    if success:
        print("\n🎉 所有测试通过!")
        return 0
    else:
        print("\n❌ 部分测试失败")
        return 1

if __name__ == '__main__':
    sys.exit(main())
