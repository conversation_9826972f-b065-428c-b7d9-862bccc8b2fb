#!/usr/bin/env python3
"""
测试TensorFlow是否可用并能加载ASTA模型

Author: Augment Agent
Date: 2025-07-31
"""

import os
import sys

def test_tensorflow():
    """测试TensorFlow是否可用"""
    try:
        print("正在导入TensorFlow...")
        import tensorflow as tf
        from tensorflow import keras
        print(f"✓ TensorFlow版本: {tf.__version__}")
        print(f"✓ Keras版本: {keras.__version__}")
        
        # 测试模型加载
        model_path = "model-best.h5"
        if os.path.exists(model_path):
            print(f"正在加载ASTA模型: {model_path}")
            model = keras.models.load_model(model_path, compile=False)
            print(f"✓ 模型加载成功")
            print(f"  输入形状: {model.input_shape}")
            print(f"  输出形状: {model.output_shape}")
            
            # 测试预测
            import numpy as np
            test_input = np.random.random((1, 528, 528, 1)).astype(np.float32)
            print("正在测试模型预测...")
            prediction = model.predict(test_input, verbose=0)
            print(f"✓ 预测成功，输出形状: {prediction.shape}")
            
            return True
        else:
            print(f"✗ 模型文件不存在: {model_path}")
            return False
            
    except ImportError as e:
        print(f"✗ TensorFlow导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_asta_detector():
    """测试ASTA检测器"""
    try:
        print("\n正在测试ASTA检测器...")
        from noise_satellite_detector import NoiseSatelliteDetector, TENSORFLOW_AVAILABLE
        
        print(f"TENSORFLOW_AVAILABLE: {TENSORFLOW_AVAILABLE}")
        
        # 配置使用真实ASTA模型
        config = {
            'use_asta_method': True,
            'asta_use_real_model': True,
            'asta_model_path': 'model-best.h5',
            'asta_unet_threshold': 0.5,
            'asta_batch_size': 1  # 小批次测试
        }
        
        detector = NoiseSatelliteDetector(config=config)
        print("✓ 检测器创建成功")
        
        # 测试模型加载
        model = detector._load_asta_model()
        if model is not None:
            print("✓ ASTA模型加载成功")
            return True
        else:
            print("✗ ASTA模型加载失败")
            return False
            
    except Exception as e:
        print(f"✗ ASTA检测器测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("TensorFlow和ASTA模型测试")
    print("=" * 60)
    
    # 测试TensorFlow
    tf_success = test_tensorflow()
    
    # 测试ASTA检测器
    asta_success = test_asta_detector()
    
    print("\n" + "=" * 60)
    print("测试结果:")
    print(f"TensorFlow: {'✓ 成功' if tf_success else '✗ 失败'}")
    print(f"ASTA检测器: {'✓ 成功' if asta_success else '✗ 失败'}")
    print("=" * 60)
    
    return 0 if (tf_success and asta_success) else 1

if __name__ == '__main__':
    sys.exit(main())
