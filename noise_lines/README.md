# FITS文件孤立单像素噪点和卫星拖线检测与去除工具

这个工具专门用于处理天文FITS图像中的**孤立单像素噪点**和卫星轨迹，采用先进的**ASTA方法**（U-Net + 概率霍夫变换）进行卫星拖线检测，能够精确检测并去除这些干扰，同时最大程度保护真实的天文源。

## 功能特性

### 🔍 检测功能
- **孤立单像素噪点检测**: 基于邻域分析的精确单像素噪声检测，不会误删天文源边缘
- **ASTA卫星拖线检测**: 采用FiorenSt/ASTA项目的先进方法，结合U-Net风格检测和概率霍夫变换
- **天文源保护**: 自动识别恒星和星系，避免误删除真实天文目标

### 🎯 特别优化
- **邻域分析**: 使用十字形或方形邻域检查确保像素真正孤立
- **局部验证**: 基于局部邻域统计验证噪声强度特征
- **ASTA方法**: 多尺度U-Net风格检测 + 概率霍夫变换精化 + 轮廓分析
- **交叉拖线分离**: 使用DBSCAN聚类算法分离交叉的卫星轨迹

### 🛠️ 处理功能
- **自适应阈值**: 基于局部背景统计的智能阈值处理
- **形态学清理**: 使用开运算、闭运算等形态学操作优化检测结果
- **背景替换**: 用局部背景值平滑替换检测到的干扰区域

### 📊 输出功能
- **多格式输出**: 支持FITS和JPG格式输出
- **可视化图像**: 生成详细的检测结果对比图
- **统计报告**: 提供详细的处理统计信息
- **中间结果**: 可选保存各种掩码文件用于分析

## 安装依赖

### 基本依赖
```bash
pip install numpy scipy matplotlib opencv-python scikit-image astropy sep photutils scikit-learn
```

### 可选依赖（用于真实ASTA模型）
```bash
pip install tensorflow  # 或 tensorflow-gpu
```

**注意**:
- `scikit-learn`用于ASTA方法中的DBSCAN聚类算法，如果不安装，交叉拖线分离功能将被禁用
- `tensorflow`用于加载和运行真实的ASTA预训练模型，如果不安装，将使用模拟的ASTA方法

## 使用方法

### 1. 标准命令行使用

```bash
# 基本使用
python noise_satellite_detector.py -i image.fits

# 指定输出目录和配置
python noise_satellite_detector.py -i image.fits -o output_dir --config strict

# 仅输出FITS文件，不创建可视化
python noise_satellite_detector.py -i image.fits --fits-only --no-viz

# 仅输出可视化图像
python noise_satellite_detector.py -i image.fits --jpg-only
```

### 2. ASTA兼容接口

为了与FiorenSt/ASTA项目兼容，我们提供了完全兼容的命令行接口：

```bash
# 基本ASTA兼容使用
python asta_compatible.py model.h5 sample.fits

# 指定U-Net阈值
python asta_compatible.py model.h5 sample.fits --unet_threshold 0.75

# 保存所有结果（完全兼容ASTA命令）
python asta_compatible.py model.h5 sample.fits \
    --unet_threshold 0.75 \
    --save \
    --save_mask \
    --save_predicted_mask \
    --csv_output_dir results/csv \
    --image_output_dir results/images

# 显示处理时间
python asta_compatible.py model.h5 sample.fits --time_processing
```

**注意**:
- 如果提供的model.h5文件存在且安装了TensorFlow，将使用真实的ASTA预训练模型
- 否则将使用我们实现的模拟ASTA方法（基于传统图像处理）
- 输出格式与原始ASTA工具完全兼容

### 3. Python脚本使用

```python
from noise_satellite_detector import NoiseSatelliteDetector

# 创建检测器
detector = NoiseSatelliteDetector()

# 处理文件
result = detector.process_fits_file('image.fits', 'output_dir')

if result['success']:
    print("处理成功!")
    print(f"统计信息: {result['statistics']}")
else:
    print(f"处理失败: {result['error']}")
```

### 4. 使用真实的ASTA模型

如果您有ASTA的预训练模型文件（.h5格式），可以这样使用：

```python
from noise_satellite_detector import NoiseSatelliteDetector

# 配置使用真实ASTA模型
config = {
    'use_asta_method': True,
    'asta_use_real_model': True,
    'asta_model_path': 'path/to/your/model.h5',
    'asta_unet_threshold': 0.75,
    'asta_batch_size': 8
}

detector = NoiseSatelliteDetector(config=config)
result = detector.process_fits_file('image.fits')
```

## 配置选项

### 预设配置模式

- **default**: 默认配置，使用十字形邻域检测孤立单像素噪点 + ASTA方法检测拖线
- **strict**: 严格模式，更敏感的检测，使用更低的阈值和更严格的ASTA参数
- **gentle**: 温和模式，使用方形邻域，更保守的检测和ASTA参数

### 自定义配置参数

```python
config = {
    # 孤立单像素噪点检测参数
    'noise_sigma_threshold': 5.0,      # 噪声检测的sigma阈值
    'use_cross_kernel': True,          # 使用十字形邻域（False为方形邻域）
    'verify_noise_intensity': True,    # 验证噪声强度特征
    'local_noise_threshold': 3.0,     # 局部邻域噪声阈值

    # ASTA卫星拖线检测参数
    'use_asta_method': True,           # 使用ASTA方法
    'asta_patch_size': 528,            # ASTA图像块大小
    'asta_patch_overlap': 64,          # 图像块重叠
    'asta_unet_threshold': 0.3,        # U-Net阈值
    'asta_hough_threshold': 0.5,       # 霍夫变换阈值
    'asta_hough_votes': 50,            # 霍夫变换投票数
    'asta_line_intensity_threshold': 0.3,  # 线段强度阈值
    'asta_min_area': 50,               # 最小区域面积
    'asta_min_eccentricity': 0.8,      # 最小偏心率
    'line_min_length': 100,            # 最小线长度
    'line_aspect_ratio_min': 8,        # 最小长宽比

    # 天文源保护参数
    'source_protection_radius': 10,    # 源保护半径
    'source_min_snr': 5.0,            # 源检测最小信噪比
    'source_min_area': 5,             # 源最小面积

    # 输出参数
    'output_format': 'both',           # 'fits', 'jpg', 'both'
    'create_visualization': True,      # 创建可视化图像
    'save_intermediate': True          # 保存中间结果
}

detector = NoiseSatelliteDetector(config=config)
```

## 输出文件

处理完成后会生成以下文件：

- `*_cleaned_*.fits`: 清理后的FITS图像
- `*_mask_*.fits`: 合并的检测掩码（可选）
- `*_noise_mask_*.fits`: 噪声检测掩码（可选）
- `*_trail_mask_*.fits`: 卫星拖线检测掩码（可选）
- `*_visualization_*.jpg`: 可视化对比图像（可选）

## 测试

使用提供的测试脚本：

```bash
python test_noise_detector.py
```

测试脚本会使用指定的测试文件运行不同配置的检测，并在`test_output`目录中生成结果。

## 算法原理

### 孤立单像素噪点检测
1. 计算图像的背景统计信息（均值、标准差、中位数）
2. 使用sigma-clipping方法识别统计异常值
3. **邻域分析**: 检查每个异常像素的3x3邻域
4. **孤立性检查**: 确保只有中心像素异常，邻域像素正常
5. **局部验证**: 基于邻域统计验证像素是否真正异常
6. **结构元素选择**: 支持十字形（4邻域）或方形（8邻域）检查

### 卫星拖线检测
1. **霍夫变换方法**: 检测图像中的直线特征
2. **形态学方法**: 使用不同方向的线性结构元素检测线条
3. **后处理**: 连接断开的线段，去除小的噪声区域

### 天文源保护
1. 使用SEP库进行源检测
2. 基于信噪比和面积筛选真实天文源
3. 在源周围创建保护区域，避免误删除

### 图像清理
1. 用局部背景值替换检测到的干扰区域
2. 高斯平滑处理边界以减少人工痕迹
3. 保持原始图像的整体统计特性

## 注意事项

1. **内存使用**: 大图像可能需要较多内存，建议处理前检查可用内存
2. **处理时间**: 复杂图像的处理时间较长，特别是启用所有检测功能时
3. **参数调优**: 不同类型的图像可能需要调整检测参数以获得最佳效果
4. **天文源保护**: 工具会尽力保护真实天文源，但极端情况下可能需要手动检查

## 技术支持

如有问题或建议，请查看代码注释或联系开发者。
