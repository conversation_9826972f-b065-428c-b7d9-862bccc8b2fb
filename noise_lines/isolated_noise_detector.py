#!/usr/bin/env python3
"""
FITS文件孤立单像素噪点检测与去除工具
专门用于处理天文图像中的孤立噪声点

功能特性:
1. 孤立单像素噪点检测 - 基于邻域分析的精确单像素噪声检测
2. 自适应阈值处理 - 基于局部背景统计
3. 天文源保护 - 避免误删除恒星和星系
4. 多格式输出 - 清理后的FITS文件、掩码文件、可视化图像

特别优化:
- 专门针对孤立的单像素噪点，不会误删除天文源的边缘像素
- 使用十字形或方形邻域检查确保像素真正孤立
- 局部强度验证避免误删除真实信号

Author: Augment Agent
Date: 2025-07-31
"""

import os
import sys
import numpy as np
import logging
from datetime import datetime
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import LogNorm
import cv2
from scipy import ndimage
from scipy.ndimage import gaussian_filter, median_filter
from skimage import morphology, measure, filters
from astropy.io import fits
from astropy.stats import sigma_clipped_stats, mad_std
import sep
import warnings

# 忽略常见警告
warnings.filterwarnings('ignore', category=RuntimeWarning)
warnings.filterwarnings('ignore', category=UserWarning)


class IsolatedNoiseDetector:
    """
    孤立单像素噪点检测器

    主要功能:
    - 检测和去除孤立的单像素噪点
    - 保护天文源不被误删
    - 生成清理后的图像和掩码

    特点:
    - 专门针对孤立单像素噪点，使用邻域分析确保精确检测
    - 不会误删除天文源的边缘像素或连续结构
    - 支持十字形和方形邻域检查模式
    """
    
    def __init__(self, config=None):
        """
        初始化检测器
        
        Args:
            config (dict): 配置参数字典
        """
        # 设置日志
        self.logger = self._setup_logger()
        
        # 默认配置
        self.default_config = {
            # 孤立单像素噪点检测参数
            'noise_sigma_threshold': 5.0,     # 噪声检测的sigma阈值
            'use_cross_kernel': True,          # 使用十字形邻域（False为方形邻域）
            'verify_noise_intensity': True,    # 验证噪声强度特征
            'local_noise_threshold': 3.0,     # 局部邻域噪声阈值
            
            # 天文源保护参数
            'source_protection_radius': 10,   # 源保护半径
            'source_min_snr': 5.0,           # 源检测最小信噪比
            'source_min_area': 5,            # 源最小面积
            
            # 图像处理参数
            'morphology_kernel_size': 1,      # 形态学核大小
            'gaussian_sigma': 0.5,            # 高斯滤波sigma
            'median_filter_size': 1,          # 中值滤波大小
            
            # 输出参数
            'save_intermediate': True,        # 保存中间结果
            'create_visualization': True,     # 创建可视化图像
            'output_format': 'both'           # 'fits', 'jpg', 'both'
        }
        
        # 合并用户配置
        self.config = self.default_config.copy()
        if config:
            self.config.update(config)
        
        self.logger.info("孤立单像素噪点检测器初始化完成")
        self.logger.info(f"配置参数: {self.config}")
    
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('IsolatedNoiseDetector')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def process_fits_file(self, fits_path, output_dir=None):
        """
        处理FITS文件
        
        Args:
            fits_path (str): 输入FITS文件路径
            output_dir (str): 输出目录路径
            
        Returns:
            dict: 处理结果
        """
        try:
            self.logger.info(f"开始处理FITS文件: {fits_path}")
            
            # 设置输出目录
            if output_dir is None:
                output_dir = os.path.dirname(fits_path)
            os.makedirs(output_dir, exist_ok=True)
            
            # 加载FITS文件
            image_data, header = self._load_fits_file(fits_path)
            if image_data is None:
                return {'success': False, 'error': '无法加载FITS文件'}
            
            # 估计背景统计
            background_stats = self._estimate_background_statistics(image_data)
            
            # 检测天文源
            sources = self._detect_astronomical_sources(image_data, background_stats)
            
            # 创建源保护掩码
            source_protection_mask = self._create_source_protection_mask(
                image_data.shape, sources
            )
            
            # 检测孤立单像素噪点
            noise_mask = self._detect_isolated_noise_pixels(
                image_data, background_stats, source_protection_mask
            )
            
            # 创建清理后的图像
            cleaned_image = self._create_cleaned_image(image_data, noise_mask)
            
            # 生成输出文件
            output_files = self._save_results(
                fits_path, output_dir, cleaned_image, noise_mask, 
                header, background_stats
            )
            
            # 统计信息
            statistics = self._calculate_statistics(
                image_data, noise_mask, sources
            )
            
            self.logger.info("FITS文件处理完成")
            self.logger.info(f"统计信息: 噪声={statistics['noise_pixels']}像素({statistics['noise_percentage']:.2f}%)")
            
            return {
                'success': True,
                'statistics': statistics,
                **output_files
            }
            
        except Exception as e:
            self.logger.error(f"处理FITS文件时出错: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _load_fits_file(self, fits_path):
        """加载FITS文件"""
        try:
            self.logger.info(f"加载FITS文件: {fits_path}")
            
            with fits.open(fits_path) as hdul:
                data = hdul[0].data.astype(np.float64)
                header = hdul[0].header
            
            self.logger.info(f"图像加载成功: {data.shape}, 数据类型: {data.dtype}")
            self.logger.info(f"数据范围: [{np.min(data):.6f}, {np.max(data):.6f}]")
            
            return data, header
            
        except Exception as e:
            self.logger.error(f"加载FITS文件失败: {str(e)}")
            return None, None
    
    def _estimate_background_statistics(self, image_data):
        """估计背景统计信息"""
        try:
            self.logger.info("估计背景统计信息...")
            
            # 使用sigma-clipped统计
            mean, median, std = sigma_clipped_stats(image_data, sigma=3.0)
            
            # 使用SEP进行背景估计
            image_sep = image_data.astype(np.float32)
            bkg = sep.Background(image_sep)
            
            # MAD标准差
            mad_std_val = mad_std(image_data)
            
            background_stats = {
                'mean': mean,
                'median': median,
                'std': std,
                'mad_std': mad_std_val,
                'background_map': bkg.back(),
                'noise_map': bkg.rms()
            }
            
            self.logger.info(f"背景统计: mean={mean:.6f}, median={median:.6f}, std={std:.6f}")
            self.logger.info(f"SEP背景: {np.mean(bkg.back()):.6f}, RMS: {np.mean(bkg.rms()):.6f}")
            
            return background_stats
            
        except Exception as e:
            self.logger.error(f"估计背景统计时出错: {str(e)}")
            return None
    
    def _detect_astronomical_sources(self, image_data, background_stats):
        """检测天文源"""
        try:
            self.logger.info("检测天文源...")
            
            # 使用SEP检测源
            image_sep = image_data.astype(np.float32)
            bkg = sep.Background(image_sep)
            image_sub = image_sep - bkg.back()
            
            # 检测源
            sources = sep.extract(
                image_sub, 
                thresh=self.config['source_min_snr'], 
                err=bkg.globalrms,
                minarea=self.config['source_min_area']
            )
            
            self.logger.info(f"检测到 {len(sources)} 个天文源")
            
            return sources
            
        except Exception as e:
            self.logger.error(f"检测天文源时出错: {str(e)}")
            return []
    
    def _create_source_protection_mask(self, image_shape, sources):
        """创建源保护掩码"""
        try:
            protection_mask = np.zeros(image_shape, dtype=bool)
            
            if len(sources) > 0:
                radius = self.config['source_protection_radius']
                
                for source in sources:
                    y, x = int(source['y']), int(source['x'])
                    
                    # 创建圆形保护区域
                    yy, xx = np.ogrid[:image_shape[0], :image_shape[1]]
                    mask = (xx - x)**2 + (yy - y)**2 <= radius**2
                    protection_mask |= mask
            
            self.logger.info(f"创建了 {len(sources)} 个源的保护掩码")
            
            return protection_mask
            
        except Exception as e:
            self.logger.error(f"创建源保护掩码时出错: {str(e)}")
            return np.zeros(image_shape, dtype=bool)

    def _detect_isolated_noise_pixels(self, image_data, background_stats, source_protection_mask):
        """
        检测孤立的单像素噪点

        Args:
            image_data (np.ndarray): 输入图像数据
            background_stats (dict): 背景统计信息
            source_protection_mask (np.ndarray): 源保护掩码

        Returns:
            np.ndarray: 噪声掩码 (True表示噪声)
        """
        try:
            self.logger.info("检测孤立的单像素噪点...")

            # 计算阈值
            threshold = (background_stats['median'] +
                        self.config['noise_sigma_threshold'] * background_stats['mad_std'])

            # 找到高于阈值的像素
            high_pixels = image_data > threshold

            # 检查邻域孤立性
            if self.config['use_cross_kernel']:
                self.logger.info("使用十字形邻域检查孤立像素")
                kernel = np.array([[0, 1, 0], [1, 0, 1], [0, 1, 0]], dtype=bool)
            else:
                self.logger.info("使用方形邻域检查孤立像素")
                kernel = np.ones((3, 3), dtype=bool)
                kernel[1, 1] = False  # 排除中心像素

            # 计算邻域内高像素的数量
            neighbor_count = ndimage.convolve(high_pixels.astype(int), kernel.astype(int), mode='constant')

            # 孤立像素：自身高于阈值，但邻域内没有其他高像素
            isolated_pixels = high_pixels & (neighbor_count == 0)

            # 排除源保护区域
            isolated_pixels &= ~source_protection_mask

            self.logger.info(f"检测到 {np.sum(isolated_pixels)} 个孤立单像素噪点")

            # 强度验证
            if self.config['verify_noise_intensity']:
                isolated_pixels = self._verify_noise_intensity(
                    image_data, isolated_pixels, background_stats
                )

            return isolated_pixels

        except Exception as e:
            self.logger.error(f"检测孤立噪点时出错: {str(e)}")
            return np.zeros_like(image_data, dtype=bool)

    def _verify_noise_intensity(self, image_data, noise_candidates, background_stats):
        """验证噪声强度特征"""
        try:
            # 获取候选噪声像素的坐标
            noise_coords = np.where(noise_candidates)

            if len(noise_coords[0]) == 0:
                return noise_candidates

            verified_mask = np.zeros_like(noise_candidates, dtype=bool)
            removed_count = 0

            for i in range(len(noise_coords[0])):
                y, x = noise_coords[0][i], noise_coords[1][i]

                # 获取3x3邻域
                y_start, y_end = max(0, y-1), min(image_data.shape[0], y+2)
                x_start, x_end = max(0, x-1), min(image_data.shape[1], x+2)

                neighborhood = image_data[y_start:y_end, x_start:x_end]

                if neighborhood.size > 1:
                    # 计算邻域统计（排除中心像素）
                    center_y, center_x = y - y_start, x - x_start
                    neighbor_values = []

                    for ny in range(neighborhood.shape[0]):
                        for nx in range(neighborhood.shape[1]):
                            if ny != center_y or nx != center_x:
                                neighbor_values.append(neighborhood[ny, nx])

                    if neighbor_values:
                        neighbor_median = np.median(neighbor_values)
                        neighbor_std = np.std(neighbor_values)

                        # 检查中心像素是否显著高于邻域
                        pixel_value = image_data[y, x]
                        local_threshold = (neighbor_median +
                                         self.config['local_noise_threshold'] * neighbor_std)

                        if pixel_value > local_threshold:
                            verified_mask[y, x] = True
                        else:
                            removed_count += 1

            self.logger.info(f"强度验证移除了 {removed_count} 个疑似噪声像素")
            self.logger.info(f"经强度验证后保留 {np.sum(verified_mask)} 个孤立噪点")

            return verified_mask

        except Exception as e:
            self.logger.error(f"验证噪声强度时出错: {str(e)}")
            return noise_candidates

    def _create_cleaned_image(self, image_data, noise_mask):
        """创建清理后的图像"""
        try:
            self.logger.info("创建清理后图像...")

            cleaned_image = image_data.copy()

            if np.any(noise_mask):
                # 使用中值滤波替换噪声像素
                filtered_image = median_filter(image_data, size=3)
                cleaned_image[noise_mask] = filtered_image[noise_mask]

            self.logger.info("创建清理后图像完成")

            return cleaned_image

        except Exception as e:
            self.logger.error(f"创建清理后图像时出错: {str(e)}")
            return image_data.copy()

    def _save_results(self, fits_path, output_dir, cleaned_image, noise_mask, header, background_stats):
        """保存结果文件"""
        try:
            base_name = os.path.splitext(os.path.basename(fits_path))[0]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            output_files = {}

            # 保存清理后的FITS文件
            if self.config['output_format'] in ['fits', 'both']:
                cleaned_fits_path = os.path.join(
                    output_dir, f"{base_name}_cleaned_{timestamp}.fits"
                )

                # 更新header
                header['HISTORY'] = f'Isolated noise pixels removed by IsolatedNoiseDetector'
                header['HISTORY'] = f'Noise pixels removed: {np.sum(noise_mask)}'

                fits.writeto(cleaned_fits_path, cleaned_image, header, overwrite=True)
                output_files['cleaned_fits'] = cleaned_fits_path
                self.logger.info(f"保存清理后FITS文件: {cleaned_fits_path}")

            # 保存中间掩码文件
            if self.config['save_intermediate']:
                # 噪声掩码
                noise_mask_path = os.path.join(
                    output_dir, f"{base_name}_noise_mask_{timestamp}.fits"
                )
                fits.writeto(noise_mask_path, noise_mask.astype(np.uint8), overwrite=True)
                output_files['noise_mask'] = noise_mask_path

                self.logger.info("保存中间掩码文件完成")

            # 创建可视化
            if self.config['create_visualization']:
                viz_path = self._create_visualization(
                    fits_path, output_dir, cleaned_image, noise_mask,
                    background_stats, timestamp
                )
                if viz_path:
                    output_files['visualization'] = viz_path

            return output_files

        except Exception as e:
            self.logger.error(f"保存结果时出错: {str(e)}")
            return {}

    def _create_visualization(self, fits_path, output_dir, cleaned_image, noise_mask, background_stats, timestamp):
        """创建可视化图像"""
        try:
            # 加载原始图像用于对比
            original_data, _ = self._load_fits_file(fits_path)
            if original_data is None:
                return None

            base_name = os.path.splitext(os.path.basename(fits_path))[0]
            viz_path = os.path.join(output_dir, f"{base_name}_visualization_{timestamp}.jpg")

            # 创建可视化
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('孤立单像素噪点检测结果', fontsize=16, fontweight='bold')

            # 计算显示范围
            vmin = np.percentile(original_data, 1)
            vmax = np.percentile(original_data, 99)

            # 原始图像
            im1 = axes[0, 0].imshow(original_data, cmap='gray', vmin=vmin, vmax=vmax)
            axes[0, 0].set_title('原始图像')
            axes[0, 0].axis('off')
            plt.colorbar(im1, ax=axes[0, 0], fraction=0.046, pad=0.04)

            # 清理后图像
            im2 = axes[0, 1].imshow(cleaned_image, cmap='gray', vmin=vmin, vmax=vmax)
            axes[0, 1].set_title('清理后图像')
            axes[0, 1].axis('off')
            plt.colorbar(im2, ax=axes[0, 1], fraction=0.046, pad=0.04)

            # 差异图像
            diff_image = original_data - cleaned_image
            im3 = axes[1, 0].imshow(diff_image, cmap='RdBu_r', vmin=-100, vmax=100)
            axes[1, 0].set_title('差异图像（原始 - 清理后）')
            axes[1, 0].axis('off')
            plt.colorbar(im3, ax=axes[1, 0], fraction=0.046, pad=0.04)

            # 噪声掩码叠加
            axes[1, 1].imshow(original_data, cmap='gray', vmin=vmin, vmax=vmax)
            axes[1, 1].imshow(noise_mask, cmap='Reds', alpha=0.5)
            axes[1, 1].set_title(f'噪声检测掩码 ({np.sum(noise_mask)} 像素)')
            axes[1, 1].axis('off')

            plt.tight_layout()
            plt.savefig(viz_path, dpi=150, bbox_inches='tight')
            plt.close()

            self.logger.info(f"保存可视化图像: {viz_path}")

            return viz_path

        except Exception as e:
            self.logger.error(f"创建可视化时出错: {str(e)}")
            return None

    def _calculate_statistics(self, image_data, noise_mask, sources):
        """计算统计信息"""
        try:
            total_pixels = image_data.size
            noise_pixels = np.sum(noise_mask)

            statistics = {
                'total_pixels': total_pixels,
                'noise_pixels': noise_pixels,
                'noise_percentage': (noise_pixels / total_pixels) * 100,
                'sources_detected': len(sources)
            }

            return statistics

        except Exception as e:
            self.logger.error(f"计算统计信息时出错: {str(e)}")
            return {}


def main():
    """主函数 - 命令行接口"""
    import argparse

    parser = argparse.ArgumentParser(
        description='FITS文件孤立单像素噪点检测与去除工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python isolated_noise_detector.py -i image.fits
  python isolated_noise_detector.py -i image.fits -o output_dir --config strict
  python isolated_noise_detector.py -i image.fits --fits-only --no-viz

预设配置:
  default: 默认配置，使用十字形邻域检测
  strict:  严格模式，更敏感的检测
  gentle:  温和模式，使用方形邻域，更保守
        """
    )

    parser.add_argument('-i', '--input', required=True,
                       help='输入FITS文件路径')
    parser.add_argument('-o', '--output',
                       help='输出目录路径（默认为输入文件所在目录）')
    parser.add_argument('--config', choices=['default', 'strict', 'gentle'],
                       default='default',
                       help='预设配置模式（默认: default）')
    parser.add_argument('--fits-only', action='store_true',
                       help='仅输出FITS文件')
    parser.add_argument('--jpg-only', action='store_true',
                       help='仅输出可视化图像')
    parser.add_argument('--no-viz', action='store_true',
                       help='不创建可视化图像')
    parser.add_argument('--no-intermediate', action='store_true',
                       help='不保存中间结果文件')

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        return 1

    # 预设配置
    configs = {
        'default': {
            'noise_sigma_threshold': 5.0,
            'use_cross_kernel': True,
            'verify_noise_intensity': True,
            'local_noise_threshold': 3.0,
            'source_min_snr': 5.0,
            'source_protection_radius': 10,
            'gaussian_sigma': 0.5
        },
        'strict': {
            'noise_sigma_threshold': 4.0,
            'use_cross_kernel': True,
            'verify_noise_intensity': True,
            'local_noise_threshold': 2.5,
            'source_min_snr': 6.0,
            'source_protection_radius': 8,
            'gaussian_sigma': 0.3
        },
        'gentle': {
            'noise_sigma_threshold': 6.0,
            'use_cross_kernel': False,  # 使用方形邻域
            'verify_noise_intensity': True,
            'local_noise_threshold': 4.0,
            'source_min_snr': 4.0,
            'source_protection_radius': 12,
            'gaussian_sigma': 0.7
        }
    }

    # 获取配置
    config = configs[args.config].copy()

    # 根据命令行参数调整配置
    if args.fits_only:
        config['output_format'] = 'fits'
        config['create_visualization'] = False
    elif args.jpg_only:
        config['output_format'] = 'jpg'

    if args.no_viz:
        config['create_visualization'] = False

    if args.no_intermediate:
        config['save_intermediate'] = False

    print("=" * 60)
    print("FITS文件孤立单像素噪点检测与去除工具")
    print("=" * 60)
    print(f"输入文件: {args.input}")
    print(f"输出目录: {args.output or '输入文件所在目录'}")
    print(f"配置模式: {args.config}")
    print("-" * 60)

    # 创建检测器
    detector = IsolatedNoiseDetector(config=config)

    # 处理文件
    result = detector.process_fits_file(args.input, args.output)

    if result['success']:
        print("\n✓ 处理完成!")
        print(f"统计信息:")
        stats = result['statistics']
        print(f"  总像素数: {stats['total_pixels']:,}")
        print(f"  噪声像素: {stats['noise_pixels']:,} ({stats['noise_percentage']:.4f}%)")
        print(f"  天文源数: {stats['sources_detected']}")

        print(f"\n输出文件:")
        for key, path in result.items():
            if key != 'success' and key != 'statistics':
                print(f"  {key}: {path}")

        return 0
    else:
        print(f"\n✗ 处理失败: {result['error']}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
