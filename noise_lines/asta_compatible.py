#!/usr/bin/env python3
"""
ASTA兼容接口
模拟FiorenSt/ASTA的命令行接口，使用我们的检测器

使用方法:
python asta_compatible.py model.h5 sample.fits --unet_threshold 0.75 --save --save_mask --save_predicted_mask --csv_output_dir results/csv --image_output_dir results/images

Author: Augment Agent
Date: 2025-07-31
"""

import os
import sys
import argparse
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path
import matplotlib.pyplot as plt
from noise_satellite_detector import NoiseSatelliteDetector
from astropy.io import fits
import warnings

warnings.filterwarnings('ignore')


class ASTACompatible:
    """
    ASTA兼容接口类
    提供与FiorenSt/ASTA相同的命令行接口
    """
    
    def __init__(self):
        self.detector = None
        self.results = []
    
    def process_fits_file(self, model_path, fits_path, args):
        """
        处理FITS文件（ASTA兼容方式）
        
        Args:
            model_path (str): 模型文件路径
            fits_path (str): FITS文件路径
            args: 命令行参数
            
        Returns:
            dict: 处理结果
        """
        try:
            print(f"Processing: {fits_path}")
            print(f"Model: {model_path}")
            print(f"U-Net threshold: {args.unet_threshold}")
            
            # 配置检测器
            config = {
                'use_asta_method': True,
                'asta_use_real_model': os.path.exists(model_path) and model_path.endswith('.h5'),
                'asta_model_path': model_path if os.path.exists(model_path) else None,
                'asta_unet_threshold': args.unet_threshold,
                'asta_patch_size': 528,
                'asta_batch_size': 8,
                'output_format': 'both',
                'create_visualization': True,
                'save_intermediate': True
            }
            
            # 创建检测器
            self.detector = NoiseSatelliteDetector(config=config)
            
            # 处理文件
            result = self.detector.process_fits_file(fits_path)
            
            if not result['success']:
                print(f"Error processing file: {result.get('error', 'Unknown error')}")
                return None
            
            # 提取结果信息
            stats = result['statistics']
            
            # 创建结果记录
            result_record = {
                'filename': os.path.basename(fits_path),
                'trail_pixels': stats['trail_pixels'],
                'noise_pixels': stats['noise_pixels'],
                'total_pixels': stats['total_pixels'],
                'trail_percentage': stats['trail_percentage'],
                'noise_percentage': stats['noise_percentage'],
                'sources_detected': stats['sources_detected'],
                'processing_time': datetime.now().isoformat()
            }
            
            self.results.append(result_record)
            
            # 保存文件（如果请求）
            if args.save or args.save_mask or args.save_predicted_mask:
                self._save_asta_outputs(fits_path, result, args)
            
            print(f"✓ Processing completed successfully")
            print(f"  Trail pixels: {stats['trail_pixels']} ({stats['trail_percentage']:.2f}%)")
            print(f"  Noise pixels: {stats['noise_pixels']} ({stats['noise_percentage']:.2f}%)")
            print(f"  Sources detected: {stats['sources_detected']}")
            
            return result_record
            
        except Exception as e:
            print(f"Error processing {fits_path}: {str(e)}")
            return None
    
    def _save_asta_outputs(self, fits_path, result, args):
        """
        保存ASTA格式的输出文件
        
        Args:
            fits_path (str): 输入FITS文件路径
            result (dict): 处理结果
            args: 命令行参数
        """
        try:
            base_name = os.path.splitext(os.path.basename(fits_path))[0]
            
            # 创建输出目录
            if args.csv_output_dir:
                os.makedirs(args.csv_output_dir, exist_ok=True)
            if args.image_output_dir:
                os.makedirs(args.image_output_dir, exist_ok=True)
            
            # 保存CSV结果
            if args.save and args.csv_output_dir:
                csv_path = os.path.join(args.csv_output_dir, f"{base_name}_results.csv")
                df = pd.DataFrame([self.results[-1]])  # 最新的结果
                df.to_csv(csv_path, index=False)
                print(f"Saved CSV results: {csv_path}")
            
            # 保存掩码图像
            if args.save_mask and args.image_output_dir:
                # 这里需要从result中获取掩码信息
                # 由于我们的检测器返回的是文件路径，我们需要读取掩码文件
                self._save_mask_images(fits_path, result, args.image_output_dir, base_name)
            
            # 保存预测掩码
            if args.save_predicted_mask and args.image_output_dir:
                self._save_predicted_mask_images(fits_path, result, args.image_output_dir, base_name)
            
        except Exception as e:
            print(f"Error saving ASTA outputs: {str(e)}")
    
    def _save_mask_images(self, fits_path, result, output_dir, base_name):
        """保存掩码图像"""
        try:
            # 查找生成的掩码文件
            for key, file_path in result.items():
                if 'mask' in key and file_path.endswith('.fits'):
                    # 读取FITS掩码文件
                    with fits.open(file_path) as hdul:
                        mask_data = hdul[0].data
                    
                    # 保存为PNG
                    png_path = os.path.join(output_dir, f"{base_name}_{key}.png")
                    plt.figure(figsize=(10, 8))
                    plt.imshow(mask_data, cmap='gray')
                    plt.title(f'{key.replace("_", " ").title()}')
                    plt.axis('off')
                    plt.savefig(png_path, dpi=150, bbox_inches='tight')
                    plt.close()
                    
                    print(f"Saved mask image: {png_path}")
                    
        except Exception as e:
            print(f"Error saving mask images: {str(e)}")
    
    def _save_predicted_mask_images(self, fits_path, result, output_dir, base_name):
        """保存预测掩码图像"""
        try:
            # 查找可视化文件
            if 'visualization' in result:
                viz_path = result['visualization']
                if os.path.exists(viz_path):
                    # 复制可视化文件到输出目录
                    import shutil
                    dest_path = os.path.join(output_dir, f"{base_name}_predicted_mask.jpg")
                    shutil.copy2(viz_path, dest_path)
                    print(f"Saved predicted mask visualization: {dest_path}")
                    
        except Exception as e:
            print(f"Error saving predicted mask images: {str(e)}")


def main():
    """主函数 - ASTA兼容命令行接口"""
    parser = argparse.ArgumentParser(
        description='ASTA兼容的卫星拖线检测工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python asta_compatible.py model.h5 sample.fits --unet_threshold 0.75 --save --save_mask --save_predicted_mask --csv_output_dir results/csv --image_output_dir results/images

注意:
  - 如果model.h5文件存在且安装了TensorFlow，将使用真实的ASTA模型
  - 否则将使用模拟的ASTA方法（基于传统图像处理）
  - 输出格式与原始ASTA工具兼容
        """
    )
    
    parser.add_argument('model_path', help='ASTA模型文件路径 (.h5)')
    parser.add_argument('fits_file', help='输入FITS文件路径')
    parser.add_argument('--unet_threshold', type=float, default=0.5,
                       help='U-Net预测掩码的阈值 (默认: 0.5)')
    parser.add_argument('--save', action='store_true',
                       help='保存结果DataFrame到CSV文件')
    parser.add_argument('--save_mask', action='store_true',
                       help='保存二值掩码图像为PNG文件')
    parser.add_argument('--save_predicted_mask', action='store_true',
                       help='保存预测掩码图像为PNG文件')
    parser.add_argument('--csv_output_dir', default='.',
                       help='保存CSV结果文件的目录 (默认: 当前目录)')
    parser.add_argument('--image_output_dir', default='.',
                       help='保存掩码图像的目录 (默认: 当前目录)')
    parser.add_argument('--time_processing', action='store_true',
                       help='输出每个步骤的处理时间')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.fits_file):
        print(f"错误: FITS文件不存在: {args.fits_file}")
        return 1
    
    # 检查模型文件
    if not os.path.exists(args.model_path):
        print(f"警告: 模型文件不存在: {args.model_path}")
        print("将使用模拟的ASTA方法")
    
    print("=" * 60)
    print("ASTA兼容的卫星拖线检测工具")
    print("=" * 60)
    
    # 创建ASTA兼容处理器
    asta = ASTACompatible()
    
    # 记录开始时间
    start_time = datetime.now()
    
    # 处理文件
    result = asta.process_fits_file(args.model_path, args.fits_file, args)
    
    # 记录结束时间
    end_time = datetime.now()
    processing_time = (end_time - start_time).total_seconds()
    
    if args.time_processing:
        print(f"\n处理时间: {processing_time:.2f} 秒")
    
    if result:
        print("\n✓ 处理完成!")
        return 0
    else:
        print("\n✗ 处理失败!")
        return 1


if __name__ == '__main__':
    sys.exit(main())
