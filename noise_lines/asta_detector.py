#!/usr/bin/env python3
"""
ASTA卫星拖线检测器
基于FiorenSt/ASTA项目的卫星拖线检测工具

功能特性:
1. 真实ASTA模型支持 - 使用预训练的U-Net模型
2. 模拟ASTA方法 - 基于传统图像处理的高质量模拟
3. 概率霍夫变换精化 - 填补断开的拖线
4. 轮廓分析和特征提取 - 基于几何特征过滤
5. 交叉拖线分离 - 使用DBSCAN聚类算法

Author: Augment Agent
Date: 2025-07-31
"""

import os
import sys
import numpy as np
import logging
from datetime import datetime
from pathlib import Path
import matplotlib.pyplot as plt
import cv2
from scipy import ndimage
from scipy.ndimage import gaussian_filter
from skimage import morphology, measure, filters
from astropy.io import fits
from astropy.stats import sigma_clipped_stats, mad_std
import sep
import warnings

# 尝试导入sklearn（用于DBSCAN聚类）
try:
    from sklearn.cluster import DBSCAN
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("警告: sklearn未安装，交叉拖线分离功能将被禁用")

# 尝试导入TensorFlow/Keras（用于真实的ASTA模型）
try:
    import tensorflow as tf
    # 尝试不同的Keras导入方式
    try:
        from tensorflow import keras
    except ImportError:
        try:
            import keras
        except ImportError:
            # 对于TensorFlow 2.12，可能需要这样导入
            import tensorflow.keras as keras

    TENSORFLOW_AVAILABLE = True
    # 只在初始化时打印一次
    if not hasattr(tf, '_asta_version_printed'):
        print(f"TensorFlow版本: {tf.__version__}")
        tf._asta_version_printed = True
except ImportError as e:
    TENSORFLOW_AVAILABLE = False
    print(f"警告: TensorFlow未安装，将使用模拟的ASTA方法")

# 忽略常见警告
warnings.filterwarnings('ignore', category=RuntimeWarning)
warnings.filterwarnings('ignore', category=UserWarning)


class ASTADetector:
    """
    ASTA卫星拖线检测器

    主要功能:
    - 使用真实的ASTA预训练模型或模拟方法检测卫星拖线
    - U-Net风格的多尺度特征提取
    - 概率霍夫变换精化断开的拖线
    - 轮廓分析和几何特征过滤
    - DBSCAN聚类分离交叉拖线

    特点:
    - 完全兼容FiorenSt/ASTA项目
    - 支持真实预训练模型和高质量模拟方法
    - 精确的卫星拖线检测和清理
    """
    
    def __init__(self, config=None):
        """
        初始化检测器
        
        Args:
            config (dict): 配置参数字典
        """
        # 设置日志
        self.logger = self._setup_logger()
        
        # 默认配置
        self.default_config = {
            # ASTA卫星拖线检测参数
            'use_asta_method': True,           # 使用ASTA方法
            'asta_use_real_model': False,      # 是否使用真实的ASTA模型
            'asta_model_path': None,           # ASTA预训练模型路径（.h5文件）
            'asta_patch_size': 528,            # ASTA图像块大小
            'asta_patch_overlap': 64,          # 图像块重叠
            'asta_unet_threshold': 0.5,        # U-Net阈值
            'asta_hough_threshold': 0.5,       # 霍夫变换阈值
            'asta_hough_votes': 50,            # 霍夫变换投票数
            'asta_line_intensity_threshold': 0.3,  # 线段强度阈值
            'asta_min_area': 50,               # 最小区域面积
            'asta_min_eccentricity': 0.8,      # 最小偏心率
            'asta_batch_size': 8,              # 批量大小
            'line_min_length': 100,            # 最小线长度
            'line_max_gap': 10,                # 线段间最大间隙
            'line_aspect_ratio_min': 8,        # 最小长宽比
            
            # 输出参数
            'save_intermediate': True,         # 保存中间结果
            'create_visualization': True,      # 创建可视化图像
            'output_format': 'both'            # 'fits', 'jpg', 'both'
        }
        
        # 合并用户配置
        self.config = self.default_config.copy()
        if config:
            self.config.update(config)
        
        self.logger.info("ASTA卫星拖线检测器初始化完成")
        self.logger.info(f"TensorFlow可用: {TENSORFLOW_AVAILABLE}")
        self.logger.info(f"sklearn可用: {SKLEARN_AVAILABLE}")
    
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('ASTADetector')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def process_fits_file(self, fits_path, output_dir=None):
        """
        处理FITS文件
        
        Args:
            fits_path (str): 输入FITS文件路径
            output_dir (str): 输出目录路径
            
        Returns:
            dict: 处理结果
        """
        try:
            self.logger.info(f"开始处理FITS文件: {fits_path}")
            
            # 设置输出目录
            if output_dir is None:
                output_dir = os.path.dirname(fits_path)
            os.makedirs(output_dir, exist_ok=True)
            
            # 加载FITS文件
            image_data, header = self._load_fits_file(fits_path)
            if image_data is None:
                return {'success': False, 'error': '无法加载FITS文件'}
            
            # 估计背景统计
            background_stats = self._estimate_background_statistics(image_data)
            
            # 检测卫星拖线
            trail_mask = self._detect_satellite_trails(image_data, background_stats)
            
            # 创建清理后的图像
            cleaned_image = self._create_cleaned_image(image_data, trail_mask)
            
            # 生成输出文件
            output_files = self._save_results(
                fits_path, output_dir, cleaned_image, trail_mask, 
                header, background_stats
            )
            
            # 统计信息
            statistics = self._calculate_statistics(image_data, trail_mask)
            
            self.logger.info("FITS文件处理完成")
            self.logger.info(f"统计信息: 拖线={statistics['trail_pixels']}像素({statistics['trail_percentage']:.2f}%)")
            
            return {
                'success': True,
                'statistics': statistics,
                **output_files
            }
            
        except Exception as e:
            self.logger.error(f"处理FITS文件时出错: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _load_fits_file(self, fits_path):
        """加载FITS文件"""
        try:
            self.logger.info(f"加载FITS文件: {fits_path}")
            
            with fits.open(fits_path) as hdul:
                data = hdul[0].data.astype(np.float64)
                header = hdul[0].header
            
            self.logger.info(f"图像加载成功: {data.shape}, 数据类型: {data.dtype}")
            self.logger.info(f"数据范围: [{np.min(data):.6f}, {np.max(data):.6f}]")
            
            return data, header
            
        except Exception as e:
            self.logger.error(f"加载FITS文件失败: {str(e)}")
            return None, None
    
    def _estimate_background_statistics(self, image_data):
        """估计背景统计信息"""
        try:
            self.logger.info("估计背景统计信息...")
            
            # 使用sigma-clipped统计
            mean, median, std = sigma_clipped_stats(image_data, sigma=3.0)
            
            # 使用SEP进行背景估计
            image_sep = image_data.astype(np.float32)
            bkg = sep.Background(image_sep)
            
            # MAD标准差
            mad_std_val = mad_std(image_data)
            
            background_stats = {
                'mean': mean,
                'median': median,
                'std': std,
                'mad_std': mad_std_val,
                'background_map': bkg.back(),
                'noise_map': bkg.rms()
            }
            
            self.logger.info(f"背景统计: mean={mean:.6f}, median={median:.6f}, std={std:.6f}")
            
            return background_stats
            
        except Exception as e:
            self.logger.error(f"估计背景统计时出错: {str(e)}")
            return None
    
    def _detect_satellite_trails(self, image_data, background_stats):
        """
        使用ASTA方法检测卫星拖线
        
        Args:
            image_data (np.ndarray): 输入图像数据
            background_stats (dict): 背景统计信息
            
        Returns:
            np.ndarray: 卫星拖线掩码 (True表示拖线)
        """
        try:
            self.logger.info("使用ASTA方法检测卫星拖线...")
            
            # 检查是否使用真实的ASTA模型
            if (self.config.get('asta_use_real_model', False) and 
                self.config.get('asta_model_path') and 
                TENSORFLOW_AVAILABLE):
                return self._detect_trails_real_asta_model(image_data, background_stats)
            else:
                return self._detect_trails_simulated_asta(image_data, background_stats)
            
        except Exception as e:
            self.logger.error(f"ASTA方法检测时出错: {str(e)}")
            return np.zeros_like(image_data, dtype=bool)

    def _detect_trails_real_asta_model(self, image_data, background_stats):
        """使用真实的ASTA预训练模型进行检测"""
        try:
            self.logger.info("使用真实的ASTA预训练模型...")

            # 加载预训练模型
            model = self._load_asta_model()
            if model is None:
                self.logger.warning("无法加载ASTA模型，回退到模拟方法")
                return self._detect_trails_simulated_asta(image_data, background_stats)

            # 步骤1: 图像预处理和分块
            patches, patch_positions = self._prepare_patches_for_real_asta(image_data, background_stats)

            # 步骤2: 使用真实的U-Net模型进行预测
            unet_predictions = self._predict_with_real_unet(model, patches)

            # 步骤3: 重组预测结果
            combined_prediction = self._combine_patch_predictions(
                unet_predictions, patch_positions, image_data.shape
            )

            # 步骤4: 概率霍夫变换精化
            refined_mask = self._probabilistic_hough_refinement(combined_prediction)

            # 步骤5: 轮廓分析和特征提取
            final_mask = self._contour_analysis_and_filtering(refined_mask, image_data)

            trail_count = np.sum(final_mask)
            self.logger.info(f"真实ASTA模型检测到卫星拖线像素数: {trail_count}")

            return final_mask

        except Exception as e:
            self.logger.error(f"真实ASTA模型检测时出错: {str(e)}")
            self.logger.info("回退到模拟ASTA方法")
            return self._detect_trails_simulated_asta(image_data, background_stats)

    def _detect_trails_simulated_asta(self, image_data, background_stats):
        """使用模拟的ASTA方法（基于传统图像处理）"""
        try:
            self.logger.info("使用模拟的ASTA方法...")

            # 步骤1: 图像预处理和分块
            patches, patch_positions = self._prepare_patches_for_unet(image_data, background_stats)

            # 步骤2: 使用模拟的U-Net风格检测
            unet_predictions = self._unet_style_detection(patches)

            # 步骤3: 重组预测结果
            combined_prediction = self._combine_patch_predictions(
                unet_predictions, patch_positions, image_data.shape
            )

            # 步骤4: 概率霍夫变换精化
            refined_mask = self._probabilistic_hough_refinement(combined_prediction)

            # 步骤5: 轮廓分析和特征提取
            final_mask = self._contour_analysis_and_filtering(refined_mask, image_data)

            trail_count = np.sum(final_mask)
            self.logger.info(f"模拟ASTA方法检测到卫星拖线像素数: {trail_count}")

            return final_mask

        except Exception as e:
            self.logger.error(f"模拟ASTA方法检测时出错: {str(e)}")
            return np.zeros_like(image_data, dtype=bool)

    def _load_asta_model(self):
        """加载ASTA预训练模型"""
        try:
            model_path = self.config.get('asta_model_path')
            if not model_path or not os.path.exists(model_path):
                self.logger.error(f"ASTA模型文件不存在: {model_path}")
                return None

            if not TENSORFLOW_AVAILABLE:
                self.logger.error("TensorFlow未安装，无法加载ASTA模型")
                return None

            self.logger.info(f"加载ASTA模型: {model_path}")

            # 尝试不同的加载方法来解决兼容性问题
            try:
                # 方法1: 标准加载
                model = keras.models.load_model(model_path, compile=False)
            except Exception as e1:
                self.logger.warning(f"标准加载失败: {str(e1)}")
                try:
                    # 方法2: 使用自定义对象
                    custom_objects = {
                        'LeakyReLU': keras.layers.LeakyReLU,
                        'leaky_relu': keras.layers.LeakyReLU
                    }
                    model = keras.models.load_model(model_path, compile=False, custom_objects=custom_objects)
                except Exception as e2:
                    self.logger.warning(f"自定义对象加载失败: {str(e2)}")
                    try:
                        # 方法3: 使用tf.keras
                        import tensorflow as tf
                        model = tf.keras.models.load_model(model_path, compile=False)
                    except Exception as e3:
                        self.logger.error(f"所有加载方法都失败: {str(e3)}")
                        return None

            self.logger.info(f"ASTA模型加载成功，输入形状: {model.input_shape}")
            self.logger.info(f"ASTA模型输出形状: {model.output_shape}")

            return model

        except Exception as e:
            self.logger.error(f"加载ASTA模型时出错: {str(e)}")
            return None

    def _prepare_patches_for_real_asta(self, image_data, background_stats):
        """为真实ASTA模型准备图像块"""
        try:
            patch_size = self.config.get('asta_patch_size', 528)
            overlap = self.config.get('asta_patch_overlap', 64)

            patches = []
            patch_positions = []

            h, w = image_data.shape
            step = patch_size - overlap

            # ASTA标准化：减去背景并标准化
            image_sub = image_data - background_stats['background_map']

            # 使用robust统计进行标准化
            median = background_stats['median']
            mad_std_val = background_stats['mad_std']

            # 标准化到合适的范围
            normalized_image = np.clip((image_sub - median) / (3 * mad_std_val), -3, 3)

            # 转换到[0, 1]范围（ASTA模型期望的输入）
            normalized_image = (normalized_image + 3) / 6

            for y in range(0, h - patch_size + 1, step):
                for x in range(0, w - patch_size + 1, step):
                    # 提取图像块
                    patch = normalized_image[y:y+patch_size, x:x+patch_size]

                    # 确保patch是正确的形状
                    if patch.shape == (patch_size, patch_size):
                        # 添加通道维度（ASTA模型期望单通道输入）
                        patch = np.expand_dims(patch, axis=-1)
                        patches.append(patch)
                        patch_positions.append((y, x))

            # 处理边界区域
            if h % step != 0:
                y = h - patch_size
                for x in range(0, w - patch_size + 1, step):
                    patch = normalized_image[y:y+patch_size, x:x+patch_size]
                    if patch.shape == (patch_size, patch_size):
                        patch = np.expand_dims(patch, axis=-1)
                        patches.append(patch)
                        patch_positions.append((y, x))

            if w % step != 0:
                x = w - patch_size
                for y in range(0, h - patch_size + 1, step):
                    patch = normalized_image[y:y+patch_size, x:x+patch_size]
                    if patch.shape == (patch_size, patch_size):
                        patch = np.expand_dims(patch, axis=-1)
                        patches.append(patch)
                        patch_positions.append((y, x))

            self.logger.info(f"为真实ASTA模型创建了 {len(patches)} 个 {patch_size}x{patch_size} 的图像块")

            return patches, patch_positions

        except Exception as e:
            self.logger.error(f"为真实ASTA模型准备图像块时出错: {str(e)}")
            return [], []

    def _predict_with_real_unet(self, model, patches):
        """使用真实的U-Net模型进行预测"""
        try:
            if not patches:
                return []

            # 转换为numpy数组
            patches_array = np.array(patches)

            self.logger.info(f"使用真实U-Net模型预测 {len(patches)} 个图像块")
            self.logger.info(f"输入形状: {patches_array.shape}")

            # 批量预测
            batch_size = self.config.get('asta_batch_size', 8)
            predictions = []

            for i in range(0, len(patches_array), batch_size):
                batch = patches_array[i:i+batch_size]
                batch_predictions = model.predict(batch, verbose=0)

                # 如果输出有多个通道，取第一个通道
                if len(batch_predictions.shape) == 4 and batch_predictions.shape[-1] > 1:
                    batch_predictions = batch_predictions[..., 0]

                # 移除批次和通道维度
                for j in range(batch_predictions.shape[0]):
                    pred = batch_predictions[j]
                    if len(pred.shape) == 3:
                        pred = pred[..., 0]  # 移除通道维度
                    predictions.append(pred)

            self.logger.info(f"完成 {len(predictions)} 个图像块的真实U-Net预测")

            return predictions

        except Exception as e:
            self.logger.error(f"使用真实U-Net模型预测时出错: {str(e)}")
            return []

    def _prepare_patches_for_unet(self, image_data, background_stats):
        """为模拟U-Net准备图像块"""
        try:
            patch_size = self.config.get('asta_patch_size', 528)
            overlap = self.config.get('asta_patch_overlap', 64)

            patches = []
            patch_positions = []

            h, w = image_data.shape
            step = patch_size - overlap

            # 标准化图像
            normalized_image = self._normalize_for_detection(image_data, background_stats)

            for y in range(0, h - patch_size + 1, step):
                for x in range(0, w - patch_size + 1, step):
                    # 提取图像块
                    patch = normalized_image[y:y+patch_size, x:x+patch_size]
                    patches.append(patch)
                    patch_positions.append((y, x))

            # 处理边界区域
            if h % step != 0:
                y = h - patch_size
                for x in range(0, w - patch_size + 1, step):
                    patch = normalized_image[y:y+patch_size, x:x+patch_size]
                    patches.append(patch)
                    patch_positions.append((y, x))

            if w % step != 0:
                x = w - patch_size
                for y in range(0, h - patch_size + 1, step):
                    patch = normalized_image[y:y+patch_size, x:x+patch_size]
                    patches.append(patch)
                    patch_positions.append((y, x))

            self.logger.info(f"创建了 {len(patches)} 个 {patch_size}x{patch_size} 的图像块")

            return patches, patch_positions

        except Exception as e:
            self.logger.error(f"准备图像块时出错: {str(e)}")
            return [], []

    def _normalize_for_detection(self, image_data, background_stats):
        """为检测标准化图像"""
        try:
            # 减去背景
            image_sub = image_data - background_stats['background_map']

            # 使用robust统计进行标准化
            median = background_stats['median']
            mad_std_val = background_stats['mad_std']

            # 标准化到[-1, 1]范围，然后映射到[0, 1]
            normalized = np.clip((image_sub - median) / (3 * mad_std_val), -1, 1)
            normalized = (normalized + 1) / 2  # 映射到[0, 1]

            return normalized.astype(np.float32)

        except Exception as e:
            self.logger.error(f"标准化图像时出错: {str(e)}")
            return image_data.astype(np.float32)

    def _unet_style_detection(self, patches):
        """U-Net风格的卫星拖线检测（使用传统图像处理模拟）"""
        try:
            predictions = []

            for i, patch in enumerate(patches):
                # 模拟U-Net的多尺度特征提取
                prediction = self._simulate_unet_prediction(patch)
                predictions.append(prediction)

            self.logger.info(f"完成 {len(patches)} 个图像块的U-Net风格检测")

            return predictions

        except Exception as e:
            self.logger.error(f"U-Net风格检测时出错: {str(e)}")
            return []

    def _simulate_unet_prediction(self, patch):
        """模拟U-Net预测（使用传统图像处理技术）"""
        try:
            # 转换为8位图像
            patch_8bit = (patch * 255).astype(np.uint8)

            # 多尺度线检测（模拟U-Net的编码器-解码器结构）

            # 尺度1: 细节检测
            kernel_h1 = cv2.getStructuringElement(cv2.MORPH_RECT, (15, 1))
            kernel_v1 = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 15))
            kernel_d1 = np.eye(7, dtype=np.uint8)
            kernel_d2 = np.fliplr(np.eye(7, dtype=np.uint8))

            lines_h1 = cv2.morphologyEx(patch_8bit, cv2.MORPH_OPEN, kernel_h1)
            lines_v1 = cv2.morphologyEx(patch_8bit, cv2.MORPH_OPEN, kernel_v1)
            lines_d1 = cv2.morphologyEx(patch_8bit, cv2.MORPH_OPEN, kernel_d1)
            lines_d2 = cv2.morphologyEx(patch_8bit, cv2.MORPH_OPEN, kernel_d2)

            # 尺度2: 中等尺度检测
            kernel_h2 = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 1))
            kernel_v2 = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 25))

            lines_h2 = cv2.morphologyEx(patch_8bit, cv2.MORPH_OPEN, kernel_h2)
            lines_v2 = cv2.morphologyEx(patch_8bit, cv2.MORPH_OPEN, kernel_v2)

            # 尺度3: 大尺度检测
            kernel_h3 = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
            kernel_v3 = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 40))

            lines_h3 = cv2.morphologyEx(patch_8bit, cv2.MORPH_OPEN, kernel_h3)
            lines_v3 = cv2.morphologyEx(patch_8bit, cv2.MORPH_OPEN, kernel_v3)

            # 合并多尺度检测结果
            combined = np.maximum.reduce([
                lines_h1, lines_v1, lines_d1, lines_d2,
                lines_h2, lines_v2,
                lines_h3, lines_v3
            ])

            # 应用高斯滤波模拟U-Net的平滑效果
            combined_smooth = gaussian_filter(combined.astype(np.float32), sigma=1.0)

            # 标准化到[0, 1]范围
            if np.max(combined_smooth) > 0:
                prediction = combined_smooth / np.max(combined_smooth)
            else:
                prediction = combined_smooth

            # 应用阈值和形态学操作进一步精化
            prediction = self._refine_unet_prediction(prediction)

            return prediction

        except Exception as e:
            self.logger.error(f"模拟U-Net预测时出错: {str(e)}")
            return np.zeros_like(patch)

    def _refine_unet_prediction(self, prediction):
        """精化U-Net预测结果"""
        try:
            # 应用自适应阈值
            threshold = self.config.get('asta_unet_threshold', 0.5)
            binary_pred = (prediction > threshold).astype(np.uint8)

            # 连通组件分析
            labeled_image = measure.label(binary_pred)
            regions = measure.regionprops(labeled_image)

            # 过滤区域
            refined_mask = np.zeros_like(binary_pred, dtype=bool)

            for region in regions:
                # 检查区域特征
                area = region.area
                eccentricity = region.eccentricity
                major_axis = region.major_axis_length
                minor_axis = region.minor_axis_length

                # 过滤条件（类似ASTA论文中的特征）
                if (area >= 10 and  # 最小面积
                    eccentricity >= 0.7 and  # 高偏心率
                    major_axis >= 20 and  # 最小长度
                    major_axis / max(minor_axis, 1) >= 3):  # 长宽比

                    coords = region.coords
                    refined_mask[coords[:, 0], coords[:, 1]] = True

            # 转换回概率图
            refined_prediction = refined_mask.astype(np.float32)

            # 轻微高斯平滑
            refined_prediction = gaussian_filter(refined_prediction, sigma=0.5)

            return refined_prediction

        except Exception as e:
            self.logger.error(f"精化U-Net预测时出错: {str(e)}")
            return prediction

    def _combine_patch_predictions(self, predictions, patch_positions, image_shape):
        """合并图像块预测结果"""
        try:
            patch_size = self.config.get('asta_patch_size', 528)
            combined_prediction = np.zeros(image_shape, dtype=np.float32)
            weight_map = np.zeros(image_shape, dtype=np.float32)

            for prediction, (y, x) in zip(predictions, patch_positions):
                # 创建权重图（中心权重高，边缘权重低）
                weight = self._create_patch_weight(patch_size)

                # 确保不超出边界
                end_y = min(y + patch_size, image_shape[0])
                end_x = min(x + patch_size, image_shape[1])
                pred_h = end_y - y
                pred_w = end_x - x

                # 累加预测和权重
                combined_prediction[y:end_y, x:end_x] += prediction[:pred_h, :pred_w] * weight[:pred_h, :pred_w]
                weight_map[y:end_y, x:end_x] += weight[:pred_h, :pred_w]

            # 标准化
            mask = weight_map > 0
            combined_prediction[mask] /= weight_map[mask]

            self.logger.info("完成图像块预测结果合并")

            return combined_prediction

        except Exception as e:
            self.logger.error(f"合并预测结果时出错: {str(e)}")
            return np.zeros(image_shape, dtype=np.float32)

    def _create_patch_weight(self, patch_size):
        """创建图像块权重图（中心高，边缘低）"""
        try:
            # 创建高斯权重
            center = patch_size // 2
            y, x = np.ogrid[:patch_size, :patch_size]

            # 高斯权重函数
            sigma = patch_size / 6  # 调整sigma以控制权重分布
            weight = np.exp(-((x - center)**2 + (y - center)**2) / (2 * sigma**2))

            return weight

        except Exception as e:
            self.logger.error(f"创建权重图时出错: {str(e)}")
            return np.ones((patch_size, patch_size), dtype=np.float32)

    def _probabilistic_hough_refinement(self, prediction_map):
        """概率霍夫变换精化（ASTA方法的核心）"""
        try:
            # 转换为二值图像
            threshold = self.config.get('asta_hough_threshold', 0.5)
            binary_map = (prediction_map > threshold).astype(np.uint8) * 255

            # 边缘检测
            edges = cv2.Canny(binary_map, 50, 150, apertureSize=3)

            # 概率霍夫变换参数（基于ASTA论文）
            rho = 1  # 距离分辨率
            theta = np.pi / 180  # 角度分辨率
            hough_threshold = self.config.get('asta_hough_votes', 50)
            min_line_length = self.config.get('line_min_length', 100)
            max_line_gap = self.config.get('line_max_gap', 10)

            # 概率霍夫变换
            lines = cv2.HoughLinesP(
                edges,
                rho=rho,
                theta=theta,
                threshold=hough_threshold,
                minLineLength=min_line_length,
                maxLineGap=max_line_gap
            )

            # 创建精化后的掩码
            refined_mask = np.zeros_like(binary_map, dtype=bool)

            if lines is not None:
                for line in lines:
                    x1, y1, x2, y2 = line[0]

                    # 验证线段是否符合卫星拖线特征
                    if self._validate_asta_line(x1, y1, x2, y2, prediction_map):
                        # 在掩码上绘制线段
                        cv2.line(refined_mask.astype(np.uint8), (x1, y1), (x2, y2), 1, thickness=2)

                self.logger.info(f"概率霍夫变换检测到 {len(lines)} 条线段")

            # 与原始预测结合
            combined_mask = np.logical_or(refined_mask, binary_map > 0)

            return combined_mask.astype(bool)

        except Exception as e:
            self.logger.error(f"概率霍夫变换精化时出错: {str(e)}")
            return (prediction_map > 0.5).astype(bool)

    def _validate_asta_line(self, x1, y1, x2, y2, prediction_map):
        """验证ASTA检测的线段是否为有效卫星拖线"""
        try:
            # 计算线段长度
            length = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)

            # 基本长度检查
            if length < self.config.get('line_min_length', 100):
                return False

            # 检查线段沿线的预测强度
            num_points = int(length)
            if num_points < 2:
                return False

            # 沿线段采样点
            x_points = np.linspace(x1, x2, num_points).astype(int)
            y_points = np.linspace(y1, y2, num_points).astype(int)

            # 确保点在图像范围内
            valid_mask = ((x_points >= 0) & (x_points < prediction_map.shape[1]) &
                         (y_points >= 0) & (y_points < prediction_map.shape[0]))

            if np.sum(valid_mask) < num_points * 0.8:  # 至少80%的点在图像内
                return False

            # 检查沿线的预测强度
            line_values = prediction_map[y_points[valid_mask], x_points[valid_mask]]
            mean_intensity = np.mean(line_values)

            # 强度阈值
            intensity_threshold = self.config.get('asta_line_intensity_threshold', 0.3)

            return mean_intensity >= intensity_threshold

        except Exception as e:
            self.logger.error(f"验证ASTA线段时出错: {str(e)}")
            return False

    def _contour_analysis_and_filtering(self, refined_mask, original_image):
        """轮廓分析和特征提取（ASTA方法的最后步骤）"""
        try:
            # 连通组件分析
            labeled_mask = measure.label(refined_mask)
            regions = measure.regionprops(labeled_mask, intensity_image=original_image)

            final_mask = np.zeros_like(refined_mask, dtype=bool)
            valid_trails = 0

            for region in regions:
                # 提取特征（基于ASTA论文）
                area = region.area
                eccentricity = region.eccentricity
                major_axis = region.major_axis_length
                minor_axis = region.minor_axis_length
                mean_intensity = region.mean_intensity

                # ASTA验证条件
                conditions = [
                    area >= self.config.get('asta_min_area', 50),
                    major_axis >= self.config.get('line_min_length', 100),
                    eccentricity >= self.config.get('asta_min_eccentricity', 0.8),
                    major_axis / max(minor_axis, 1) >= self.config.get('line_aspect_ratio_min', 8),
                    mean_intensity > np.percentile(original_image, 75)  # 亮度检查
                ]

                if all(conditions):
                    # 检查是否为交叉拖线
                    separated_trails = self._separate_crossing_trails(region)

                    for trail_coords in separated_trails:
                        final_mask[trail_coords[:, 0], trail_coords[:, 1]] = True
                        valid_trails += 1

            self.logger.info(f"轮廓分析后保留 {valid_trails} 条有效卫星拖线")

            return final_mask

        except Exception as e:
            self.logger.error(f"轮廓分析时出错: {str(e)}")
            return refined_mask

    def _separate_crossing_trails(self, region):
        """分离交叉的卫星拖线（基于ASTA论文的DBSCAN方法）"""
        try:
            coords = region.coords

            # 如果区域太小，直接返回
            if len(coords) < 20:
                return [coords]

            if not SKLEARN_AVAILABLE:
                # 如果sklearn不可用，直接返回原始坐标
                return [coords]

            # 计算每个像素的局部方向
            directions = []
            for i in range(len(coords)):
                # 使用邻近点计算方向
                neighbors = []
                for j in range(max(0, i-5), min(len(coords), i+6)):
                    if i != j:
                        dy = coords[j][0] - coords[i][0]
                        dx = coords[j][1] - coords[i][1]
                        if dx != 0 or dy != 0:
                            angle = np.arctan2(dy, dx)
                            neighbors.append(angle)

                if neighbors:
                    # 使用中位数角度作为该点的方向
                    median_angle = np.median(neighbors)
                    directions.append(median_angle)
                else:
                    directions.append(0)

            directions = np.array(directions)

            # 将角度转换为适合聚类的特征
            angle_features = np.column_stack([np.cos(directions), np.sin(directions)])

            # DBSCAN聚类
            clustering = DBSCAN(eps=0.3, min_samples=10).fit(angle_features)
            labels = clustering.labels_

            # 分离不同的拖线
            separated_trails = []
            unique_labels = set(labels)

            for label in unique_labels:
                if label != -1:  # 忽略噪声点
                    trail_coords = coords[labels == label]
                    if len(trail_coords) >= 20:  # 最小拖线长度
                        separated_trails.append(trail_coords)

            # 如果没有找到有效的聚类，返回原始坐标
            if not separated_trails:
                separated_trails = [coords]

            return separated_trails

        except Exception as e:
            self.logger.error(f"分离交叉拖线时出错: {str(e)}")
            return [region.coords]

    def _create_cleaned_image(self, image_data, trail_mask):
        """创建清理后的图像"""
        try:
            self.logger.info("创建清理后图像...")

            cleaned_image = image_data.copy()

            if np.any(trail_mask):
                # 使用中值滤波替换拖线像素
                from scipy.ndimage import median_filter
                filtered_image = median_filter(image_data, size=3)
                cleaned_image[trail_mask] = filtered_image[trail_mask]

            self.logger.info("创建清理后图像完成")

            return cleaned_image

        except Exception as e:
            self.logger.error(f"创建清理后图像时出错: {str(e)}")
            return image_data.copy()

    def _save_results(self, fits_path, output_dir, cleaned_image, trail_mask, header, background_stats):
        """保存结果文件"""
        try:
            base_name = os.path.splitext(os.path.basename(fits_path))[0]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            output_files = {}

            # 保存清理后的FITS文件
            if self.config['output_format'] in ['fits', 'both']:
                cleaned_fits_path = os.path.join(
                    output_dir, f"{base_name}_asta_cleaned_{timestamp}.fits"
                )

                # 更新header
                header['HISTORY'] = f'Satellite trails removed by ASTA method'
                header['HISTORY'] = f'Trail pixels removed: {np.sum(trail_mask)}'

                fits.writeto(cleaned_fits_path, cleaned_image, header, overwrite=True)
                output_files['cleaned_fits'] = cleaned_fits_path
                self.logger.info(f"保存清理后FITS文件: {cleaned_fits_path}")

            # 保存中间掩码文件
            if self.config['save_intermediate']:
                # 拖线掩码
                trail_mask_path = os.path.join(
                    output_dir, f"{base_name}_trail_mask_{timestamp}.fits"
                )
                fits.writeto(trail_mask_path, trail_mask.astype(np.uint8), overwrite=True)
                output_files['trail_mask'] = trail_mask_path

                self.logger.info("保存中间掩码文件完成")

            # 创建可视化
            if self.config['create_visualization']:
                viz_path = self._create_visualization(
                    fits_path, output_dir, cleaned_image, trail_mask,
                    background_stats, timestamp
                )
                if viz_path:
                    output_files['visualization'] = viz_path

            return output_files

        except Exception as e:
            self.logger.error(f"保存结果时出错: {str(e)}")
            return {}

    def _create_visualization(self, fits_path, output_dir, cleaned_image, trail_mask, background_stats, timestamp):
        """创建可视化图像"""
        try:
            # 加载原始图像用于对比
            original_data, _ = self._load_fits_file(fits_path)
            if original_data is None:
                return None

            base_name = os.path.splitext(os.path.basename(fits_path))[0]
            viz_path = os.path.join(output_dir, f"{base_name}_asta_visualization_{timestamp}.jpg")

            # 创建可视化
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('ASTA卫星拖线检测结果', fontsize=16, fontweight='bold')

            # 计算显示范围
            vmin = np.percentile(original_data, 1)
            vmax = np.percentile(original_data, 99)

            # 原始图像
            im1 = axes[0, 0].imshow(original_data, cmap='gray', vmin=vmin, vmax=vmax)
            axes[0, 0].set_title('原始图像')
            axes[0, 0].axis('off')
            plt.colorbar(im1, ax=axes[0, 0], fraction=0.046, pad=0.04)

            # 清理后图像
            im2 = axes[0, 1].imshow(cleaned_image, cmap='gray', vmin=vmin, vmax=vmax)
            axes[0, 1].set_title('ASTA清理后图像')
            axes[0, 1].axis('off')
            plt.colorbar(im2, ax=axes[0, 1], fraction=0.046, pad=0.04)

            # 差异图像
            diff_image = original_data - cleaned_image
            im3 = axes[1, 0].imshow(diff_image, cmap='RdBu_r', vmin=-100, vmax=100)
            axes[1, 0].set_title('差异图像（原始 - 清理后）')
            axes[1, 0].axis('off')
            plt.colorbar(im3, ax=axes[1, 0], fraction=0.046, pad=0.04)

            # 拖线掩码叠加
            axes[1, 1].imshow(original_data, cmap='gray', vmin=vmin, vmax=vmax)
            axes[1, 1].imshow(trail_mask, cmap='Blues', alpha=0.5)
            axes[1, 1].set_title(f'拖线检测掩码 ({np.sum(trail_mask)} 像素)')
            axes[1, 1].axis('off')

            plt.tight_layout()
            plt.savefig(viz_path, dpi=150, bbox_inches='tight')
            plt.close()

            self.logger.info(f"保存可视化图像: {viz_path}")

            return viz_path

        except Exception as e:
            self.logger.error(f"创建可视化时出错: {str(e)}")
            return None

    def _calculate_statistics(self, image_data, trail_mask):
        """计算统计信息"""
        try:
            total_pixels = image_data.size
            trail_pixels = np.sum(trail_mask)

            statistics = {
                'total_pixels': total_pixels,
                'trail_pixels': trail_pixels,
                'trail_percentage': (trail_pixels / total_pixels) * 100
            }

            return statistics

        except Exception as e:
            self.logger.error(f"计算统计信息时出错: {str(e)}")
            return {}


def main():
    """主函数 - 命令行接口"""
    import argparse

    parser = argparse.ArgumentParser(
        description='ASTA卫星拖线检测器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python asta_detector.py -i image.fits
  python asta_detector.py -i image.fits --model model-best.h5 --threshold 0.5
  python asta_detector.py -i image.fits --model model-best.h5 --config strict

注意:
  - 如果指定了模型文件且安装了TensorFlow，将使用真实的ASTA模型
  - 否则将使用模拟的ASTA方法（基于传统图像处理）
        """
    )

    parser.add_argument('-i', '--input', required=True,
                       help='输入FITS文件路径')
    parser.add_argument('-o', '--output',
                       help='输出目录路径（默认为输入文件所在目录）')
    parser.add_argument('--model',
                       help='ASTA预训练模型文件路径 (.h5)')
    parser.add_argument('--threshold', type=float, default=0.5,
                       help='U-Net预测阈值 (默认: 0.5)')
    parser.add_argument('--config', choices=['default', 'strict', 'gentle'],
                       default='default',
                       help='预设配置模式（默认: default）')
    parser.add_argument('--fits-only', action='store_true',
                       help='仅输出FITS文件')
    parser.add_argument('--jpg-only', action='store_true',
                       help='仅输出可视化图像')
    parser.add_argument('--no-viz', action='store_true',
                       help='不创建可视化图像')
    parser.add_argument('--no-intermediate', action='store_true',
                       help='不保存中间结果文件')
    parser.add_argument('--batch-size', type=int, default=8,
                       help='批量大小（仅用于真实模型）')

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        return 1

    # 预设配置
    configs = {
        'default': {
            'asta_unet_threshold': 0.5,
            'asta_hough_threshold': 0.5,
            'asta_hough_votes': 50,
            'line_min_length': 100,
            'line_aspect_ratio_min': 8,
            'asta_batch_size': 8
        },
        'strict': {
            'asta_unet_threshold': 0.3,
            'asta_hough_threshold': 0.4,
            'asta_hough_votes': 40,
            'line_min_length': 80,
            'line_aspect_ratio_min': 10,
            'asta_batch_size': 8
        },
        'gentle': {
            'asta_unet_threshold': 0.7,
            'asta_hough_threshold': 0.6,
            'asta_hough_votes': 60,
            'line_min_length': 150,
            'line_aspect_ratio_min': 6,
            'asta_batch_size': 8
        }
    }

    # 获取配置
    config = configs[args.config].copy()

    # 根据命令行参数调整配置
    if args.model:
        config['asta_use_real_model'] = True
        config['asta_model_path'] = args.model

    if args.threshold != 0.5:
        config['asta_unet_threshold'] = args.threshold

    if args.batch_size != 8:
        config['asta_batch_size'] = args.batch_size

    if args.fits_only:
        config['output_format'] = 'fits'
        config['create_visualization'] = False
    elif args.jpg_only:
        config['output_format'] = 'jpg'

    if args.no_viz:
        config['create_visualization'] = False

    if args.no_intermediate:
        config['save_intermediate'] = False

    print("=" * 60)
    print("ASTA卫星拖线检测器")
    print("=" * 60)
    print(f"输入文件: {args.input}")
    print(f"输出目录: {args.output or '输入文件所在目录'}")
    print(f"配置模式: {args.config}")
    if args.model:
        print(f"ASTA模型: {args.model}")
        print(f"使用真实模型: {os.path.exists(args.model) and TENSORFLOW_AVAILABLE}")
    else:
        print("ASTA模型: 使用模拟方法")
    print(f"U-Net阈值: {config['asta_unet_threshold']}")
    print("-" * 60)

    # 创建检测器
    detector = ASTADetector(config=config)

    # 处理文件
    result = detector.process_fits_file(args.input, args.output)

    if result['success']:
        print("\n✓ 处理完成!")
        print(f"统计信息:")
        stats = result['statistics']
        print(f"  总像素数: {stats['total_pixels']:,}")
        print(f"  拖线像素: {stats['trail_pixels']:,} ({stats['trail_percentage']:.4f}%)")

        print(f"\n输出文件:")
        for key, path in result.items():
            if key != 'success' and key != 'statistics':
                print(f"  {key}: {path}")

        return 0
    else:
        print(f"\n✗ 处理失败: {result['error']}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
