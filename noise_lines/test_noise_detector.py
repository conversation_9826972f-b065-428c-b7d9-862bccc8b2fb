#!/usr/bin/env python3
"""
孤立单像素噪点和卫星拖线检测器测试脚本
用于测试noise_satellite_detector.py的孤立单像素噪点检测功能

Author: Augment Agent
Date: 2025-07-31
"""

import os
import sys
from noise_satellite_detector import NoiseSatelliteDetector

def test_detector():
    """测试检测器功能"""
    
    # 测试文件路径
    test_file = r"E:\fix_data\align-compare\GY5_K053-1_No Filter_60S_Bin2_UTC20250628_193509_-14.9C_.fit"
    
    print("=" * 60)
    print("孤立单像素噪点和卫星拖线检测器测试")
    print("=" * 60)
    print(f"测试文件: {test_file}")
    
    # 检查测试文件是否存在
    if not os.path.exists(test_file):
        print(f"错误: 测试文件不存在: {test_file}")
        return False
    
    # 创建输出目录
    output_dir = "noise_lines/test_output"
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"输出目录: {output_dir}")
    print("-" * 60)
    
    try:
        # 测试默认配置（十字形邻域 + ASTA拖线检测）
        print("\n1. 测试默认配置（孤立单像素 + ASTA拖线检测）...")
        config_default = {
            'use_cross_kernel': True,
            'verify_noise_intensity': True,
            'local_noise_threshold': 3.0,
            'use_asta_method': True,
            'asta_patch_size': 528,
            'asta_unet_threshold': 0.3,
            'asta_hough_threshold': 0.5,
            'line_min_length': 100,
            'line_aspect_ratio_min': 8,
            'output_format': 'both',
            'create_visualization': True,
            'save_intermediate': True
        }
        
        detector = NoiseSatelliteDetector(config=config_default)
        result = detector.process_fits_file(test_file, output_dir)
        
        if result['success']:
            print("✓ 默认配置测试成功")
            stats = result['statistics']
            print(f"  - 检测到源: {stats['sources_detected']}")
            print(f"  - 噪声像素: {stats['noise_pixels']} ({stats['noise_percentage']:.2f}%)")
            print(f"  - 拖线像素: {stats['trail_pixels']} ({stats['trail_percentage']:.2f}%)")
            print(f"  - 保护像素: {stats['protected_pixels']} ({stats['protected_percentage']:.2f}%)")
        else:
            print(f"✗ 默认配置测试失败: {result.get('error', '未知错误')}")
            return False
        
        # 测试严格配置（更敏感的检测 + 严格ASTA标准）
        print("\n2. 测试严格配置（更敏感检测 + 严格ASTA标准）...")
        config_strict = {
            'noise_sigma_threshold': 4.0,
            'use_cross_kernel': True,
            'verify_noise_intensity': True,
            'local_noise_threshold': 2.5,
            'use_asta_method': True,
            'asta_patch_size': 528,
            'asta_unet_threshold': 0.25,
            'asta_hough_threshold': 0.4,
            'line_min_length': 80,
            'line_aspect_ratio_min': 10,
            'source_min_snr': 6.0,
            'source_protection_radius': 8,
            'output_format': 'fits',
            'create_visualization': False,
            'save_intermediate': False
        }
        
        detector_strict = NoiseSatelliteDetector(config=config_strict)
        result_strict = detector_strict.process_fits_file(test_file, output_dir)
        
        if result_strict['success']:
            print("✓ 严格配置测试成功")
            stats = result_strict['statistics']
            print(f"  - 检测到源: {stats['sources_detected']}")
            print(f"  - 噪声像素: {stats['noise_pixels']} ({stats['noise_percentage']:.2f}%)")
            print(f"  - 拖线像素: {stats['trail_pixels']} ({stats['trail_percentage']:.2f}%)")
        else:
            print(f"✗ 严格配置测试失败: {result_strict.get('error', '未知错误')}")
        
        # 测试温和配置（方形邻域 + 保守ASTA检测）
        print("\n3. 测试温和配置（方形邻域 + 保守ASTA检测）...")
        config_gentle = {
            'noise_sigma_threshold': 6.0,
            'use_cross_kernel': False,  # 使用方形邻域
            'verify_noise_intensity': True,
            'local_noise_threshold': 4.0,
            'use_asta_method': True,
            'asta_patch_size': 528,
            'asta_unet_threshold': 0.4,
            'asta_hough_threshold': 0.6,
            'line_min_length': 150,
            'line_aspect_ratio_min': 6,
            'source_min_snr': 4.0,
            'source_protection_radius': 12,
            'output_format': 'jpg',
            'create_visualization': True,
            'save_intermediate': False
        }
        
        detector_gentle = NoiseSatelliteDetector(config=config_gentle)
        result_gentle = detector_gentle.process_fits_file(test_file, output_dir)
        
        if result_gentle['success']:
            print("✓ 温和配置测试成功")
            stats = result_gentle['statistics']
            print(f"  - 检测到源: {stats['sources_detected']}")
            print(f"  - 噪声像素: {stats['noise_pixels']} ({stats['noise_percentage']:.2f}%)")
            print(f"  - 拖线像素: {stats['trail_pixels']} ({stats['trail_percentage']:.2f}%)")
        else:
            print(f"✗ 温和配置测试失败: {result_gentle.get('error', '未知错误')}")
        
        print("\n" + "=" * 60)
        print("测试完成!")
        print(f"输出文件保存在: {output_dir}")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        return False

def main():
    """主函数"""
    success = test_detector()
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
