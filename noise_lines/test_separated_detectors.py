#!/usr/bin/env python3
"""
测试拆分后的检测器
分别测试孤立噪点检测器和ASTA卫星拖线检测器

Author: Augment Agent
Date: 2025-07-31
"""

import os
import sys
import subprocess
from datetime import datetime

def test_isolated_noise_detector():
    """测试孤立噪点检测器"""
    print("=" * 60)
    print("测试孤立噪点检测器")
    print("=" * 60)
    
    test_file = r"E:\fix_data\align-compare\GY5_K053-1_No Filter_60S_Bin2_UTC20250628_193509_-14.9C_.fit"
    
    if not os.path.exists(test_file):
        print(f"错误: 测试文件不存在: {test_file}")
        return False
    
    # 创建输出目录
    output_dir = "test_results/isolated_noise"
    os.makedirs(output_dir, exist_ok=True)
    
    test_cases = [
        {
            'name': '默认配置',
            'config': 'default',
            'extra_args': []
        },
        {
            'name': '严格配置',
            'config': 'strict',
            'extra_args': []
        },
        {
            'name': '温和配置',
            'config': 'gentle',
            'extra_args': []
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 40)
        
        # 构建命令
        cmd = [
            'python', 'isolated_noise_detector.py',
            '-i', test_file,
            '-o', output_dir,
            '--config', test_case['config']
        ]
        cmd.extend(test_case['extra_args'])
        
        print(f"命令: {' '.join(cmd)}")
        
        try:
            # 执行命令
            result = subprocess.run(
                cmd,
                cwd=os.getcwd(),
                capture_output=True,
                text=True,
                timeout=180  # 3分钟超时
            )
            
            if result.returncode == 0:
                print("✓ 测试成功")
                # 提取关键信息
                lines = result.stdout.split('\n')
                for line in lines:
                    if '噪声像素:' in line or '天文源数:' in line:
                        print(f"  {line.strip()}")
                success_count += 1
            else:
                print("✗ 测试失败")
                print("错误输出:")
                print(result.stderr)
                    
        except subprocess.TimeoutExpired:
            print("✗ 测试超时")
        except Exception as e:
            print(f"✗ 测试异常: {str(e)}")
    
    print(f"\n孤立噪点检测器测试完成: {success_count}/{len(test_cases)} 个测试成功")
    return success_count == len(test_cases)

def test_asta_detector():
    """测试ASTA卫星拖线检测器"""
    print("\n" + "=" * 60)
    print("测试ASTA卫星拖线检测器")
    print("=" * 60)
    
    test_file = r"E:\fix_data\align-compare\GY5_K053-1_No Filter_60S_Bin2_UTC20250628_193509_-14.9C_.fit"
    
    if not os.path.exists(test_file):
        print(f"错误: 测试文件不存在: {test_file}")
        return False
    
    # 创建输出目录
    output_dir = "test_results/asta"
    os.makedirs(output_dir, exist_ok=True)
    
    test_cases = [
        {
            'name': '模拟ASTA方法（默认）',
            'config': 'default',
            'extra_args': []
        },
        {
            'name': '真实ASTA模型（如果可用）',
            'config': 'default',
            'extra_args': ['--model', 'model-best.h5', '--threshold', '0.5']
        },
        {
            'name': '严格配置',
            'config': 'strict',
            'extra_args': []
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 40)
        
        # 构建命令
        cmd = [
            'python', 'asta_detector.py',
            '-i', test_file,
            '-o', output_dir,
            '--config', test_case['config']
        ]
        cmd.extend(test_case['extra_args'])
        
        print(f"命令: {' '.join(cmd)}")
        
        try:
            # 执行命令
            result = subprocess.run(
                cmd,
                cwd=os.getcwd(),
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            if result.returncode == 0:
                print("✓ 测试成功")
                # 提取关键信息
                lines = result.stdout.split('\n')
                for line in lines:
                    if '拖线像素:' in line or 'TensorFlow' in line or 'ASTA模型' in line:
                        print(f"  {line.strip()}")
                success_count += 1
            else:
                print("✗ 测试失败")
                print("错误输出:")
                print(result.stderr)
                    
        except subprocess.TimeoutExpired:
            print("✗ 测试超时")
        except Exception as e:
            print(f"✗ 测试异常: {str(e)}")
    
    print(f"\nASTA检测器测试完成: {success_count}/{len(test_cases)} 个测试成功")
    return success_count == len(test_cases)

def test_combined_workflow():
    """测试组合工作流程"""
    print("\n" + "=" * 60)
    print("测试组合工作流程")
    print("=" * 60)
    
    test_file = r"E:\fix_data\align-compare\GY5_K053-1_No Filter_60S_Bin2_UTC20250628_193509_-14.9C_.fit"
    
    if not os.path.exists(test_file):
        print(f"错误: 测试文件不存在: {test_file}")
        return False
    
    # 创建输出目录
    output_dir = "test_results/combined"
    os.makedirs(output_dir, exist_ok=True)
    
    print("步骤1: 先使用孤立噪点检测器")
    print("-" * 40)
    
    # 第一步：孤立噪点检测
    cmd1 = [
        'python', 'isolated_noise_detector.py',
        '-i', test_file,
        '-o', output_dir,
        '--config', 'default'
    ]
    
    try:
        result1 = subprocess.run(cmd1, cwd=os.getcwd(), capture_output=True, text=True, timeout=180)
        
        if result1.returncode == 0:
            print("✓ 孤立噪点检测成功")
            
            # 查找清理后的文件
            import glob
            cleaned_files = glob.glob(os.path.join(output_dir, "*_cleaned_*.fits"))
            
            if cleaned_files:
                cleaned_file = cleaned_files[0]
                print(f"  清理后文件: {os.path.basename(cleaned_file)}")
                
                print("\n步骤2: 对清理后的文件使用ASTA检测器")
                print("-" * 40)
                
                # 第二步：ASTA检测
                cmd2 = [
                    'python', 'asta_detector.py',
                    '-i', cleaned_file,
                    '-o', output_dir,
                    '--config', 'default'
                ]
                
                result2 = subprocess.run(cmd2, cwd=os.getcwd(), capture_output=True, text=True, timeout=300)
                
                if result2.returncode == 0:
                    print("✓ ASTA检测成功")
                    print("✓ 组合工作流程测试成功")
                    return True
                else:
                    print("✗ ASTA检测失败")
                    print(result2.stderr)
            else:
                print("✗ 未找到清理后的文件")
        else:
            print("✗ 孤立噪点检测失败")
            print(result1.stderr)
            
    except Exception as e:
        print(f"✗ 组合工作流程测试异常: {str(e)}")
    
    return False

def show_file_structure():
    """显示生成的文件结构"""
    print("\n" + "=" * 60)
    print("生成的文件结构")
    print("=" * 60)
    
    import os
    from pathlib import Path
    
    test_results_dir = "test_results"
    if os.path.exists(test_results_dir):
        for root, dirs, files in os.walk(test_results_dir):
            level = root.replace(test_results_dir, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                print(f"{subindent}{file} ({file_size:,} bytes)")

def main():
    """主函数"""
    print("拆分后检测器测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试孤立噪点检测器
    noise_success = test_isolated_noise_detector()
    
    # 测试ASTA检测器
    asta_success = test_asta_detector()
    
    # 测试组合工作流程
    combined_success = test_combined_workflow()
    
    # 显示文件结构
    show_file_structure()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"孤立噪点检测器: {'✓ 通过' if noise_success else '✗ 失败'}")
    print(f"ASTA检测器: {'✓ 通过' if asta_success else '✗ 失败'}")
    print(f"组合工作流程: {'✓ 通过' if combined_success else '✗ 失败'}")
    
    if noise_success and asta_success:
        print("\n🎉 所有检测器都工作正常！")
        print("\n使用方法:")
        print("1. 孤立噪点检测: python isolated_noise_detector.py -i image.fits")
        print("2. ASTA拖线检测: python asta_detector.py -i image.fits --model model-best.h5")
        print("3. 组合使用: 先运行噪点检测，再对清理后的文件运行ASTA检测")
        return 0
    else:
        print("\n❌ 部分测试失败")
        return 1

if __name__ == '__main__':
    sys.exit(main())
