#!/usr/bin/env python3
"""
FITS文件孤立单像素噪点和卫星拖线检测与去除工具
专门用于处理天文图像中的孤立噪声点和卫星轨迹

功能特性:
1. 孤立单像素噪点检测 - 基于邻域分析的精确单像素噪声检测
2. 卫星拖线检测 - 霍夫变换、形态学线检测
3. 自适应阈值处理 - 基于局部背景统计
4. 天文源保护 - 避免误删除恒星和星系
5. 多格式输出 - 清理后的FITS文件、掩码文件、可视化图像

特别优化:
- 专门针对孤立的单像素噪点，不会误删除天文源的边缘像素
- 使用十字形或方形邻域检查确保像素真正孤立
- 局部强度验证避免误删除真实信号

Author: Augment Agent
Date: 2025-07-31
"""

import os
import sys
import numpy as np
import logging
from datetime import datetime
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import LogNorm
import cv2
from scipy import ndimage, signal
from scipy.ndimage import gaussian_filter, median_filter, binary_erosion, binary_dilation
from skimage import morphology, measure, filters
from skimage.transform import hough_line, hough_line_peaks
from astropy.io import fits
from astropy.stats import sigma_clipped_stats, mad_std
from astropy.convolution import Gaussian2DKernel, convolve
import sep
import warnings

# 尝试导入sklearn（用于DBSCAN聚类）
try:
    from sklearn.cluster import DBSCAN
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("警告: sklearn未安装，ASTA方法的交叉拖线分离功能将被禁用")

# 尝试导入TensorFlow/Keras（用于真实的ASTA模型）
try:
    import tensorflow as tf
    # 尝试不同的Keras导入方式
    try:
        from tensorflow import keras
    except ImportError:
        try:
            import keras
        except ImportError:
            # 对于TensorFlow 2.12，可能需要这样导入
            import tensorflow.keras as keras

    TENSORFLOW_AVAILABLE = True
    # 只在初始化时打印一次
    if not hasattr(tf, '_asta_version_printed'):
        print(f"TensorFlow版本: {tf.__version__}")
        tf._asta_version_printed = True
except ImportError as e:
    TENSORFLOW_AVAILABLE = False
    print(f"警告: TensorFlow未安装，将使用模拟的ASTA方法: {str(e)}")

# 忽略常见警告
warnings.filterwarnings('ignore', category=RuntimeWarning)
warnings.filterwarnings('ignore', category=UserWarning)


class NoiseSatelliteDetector:
    """
    孤立单像素噪点和卫星拖线检测器

    主要功能:
    - 检测和去除孤立的单像素噪点
    - 检测和去除卫星拖线
    - 保护天文源不被误删
    - 生成清理后的图像和掩码

    特点:
    - 专门针对孤立单像素噪点，使用邻域分析确保精确检测
    - 不会误删除天文源的边缘像素或连续结构
    - 支持十字形和方形邻域检查模式
    """
    
    def __init__(self, config=None):
        """
        初始化检测器
        
        Args:
            config (dict): 配置参数字典
        """
        # 设置日志
        self.setup_logging()
        
        # 默认配置参数 - 专门针对孤立单像素噪点
        self.default_config = {
            # 孤立噪声检测参数
            'noise_sigma_threshold': 5.0,      # 噪声检测的sigma阈值
            'use_cross_kernel': True,          # 使用十字形邻域（False为方形邻域）
            'verify_noise_intensity': True,    # 验证噪声强度特征
            'local_noise_threshold': 3.0,     # 局部邻域噪声阈值

            # ASTA卫星拖线检测参数
            'use_asta_method': True,           # 使用ASTA方法
            'asta_model_path': None,           # ASTA预训练模型路径（.h5文件）
            'asta_patch_size': 528,            # ASTA图像块大小
            'asta_patch_overlap': 64,          # 图像块重叠
            'asta_unet_threshold': 0.5,        # U-Net阈值（ASTA默认）
            'asta_hough_threshold': 0.5,       # 霍夫变换阈值
            'asta_hough_votes': 50,            # 霍夫变换投票数
            'asta_line_intensity_threshold': 0.3,  # 线段强度阈值
            'asta_min_area': 50,               # 最小区域面积
            'asta_min_eccentricity': 0.8,      # 最小偏心率
            'line_min_length': 100,            # 最小线长度
            'line_max_gap': 10,                # 线段间最大间隙
            'line_aspect_ratio_min': 8,        # 最小长宽比
            'asta_use_real_model': False,      # 是否使用真实的ASTA模型

            # 天文源保护参数
            'source_protection_radius': 10,    # 源保护半径
            'source_min_snr': 5.0,            # 源检测最小信噪比
            'source_min_area': 5,             # 源最小面积

            # 形态学操作参数（针对单像素处理优化）
            'morphology_kernel_size': 1,       # 形态学核大小（单像素不需要大核）
            'gaussian_sigma': 0.5,             # 轻微高斯平滑
            'median_filter_size': 1,           # 不使用中值滤波

            # 输出参数
            'save_intermediate': True,         # 保存中间结果
            'create_visualization': True,      # 创建可视化图像
            'output_format': 'both'            # 输出格式: 'fits', 'jpg', 'both'
        }
        
        # 合并用户配置
        self.config = self.default_config.copy()
        if config:
            self.config.update(config)
            
        self.logger.info("孤立单像素噪点和卫星拖线检测器初始化完成")
        self.logger.info(f"配置参数: {self.config}")
    
    def setup_logging(self):
        """设置日志系统"""
        self.logger = logging.getLogger('NoiseSatelliteDetector')
        self.logger.setLevel(logging.INFO)
        
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def load_fits_image(self, fits_path):
        """
        加载FITS图像文件
        
        Args:
            fits_path (str): FITS文件路径
            
        Returns:
            tuple: (图像数据, header信息, 是否成功)
        """
        try:
            self.logger.info(f"加载FITS文件: {fits_path}")
            
            with fits.open(fits_path) as hdul:
                header = hdul[0].header
                image_data = hdul[0].data
                
                if image_data is None:
                    self.logger.error(f"无法读取图像数据: {fits_path}")
                    return None, None, False
                
                # 处理3D数据（取第一个切片）
                if len(image_data.shape) == 3:
                    image_data = image_data[0]
                    self.logger.info("检测到3D数据，使用第一个切片")
                
                # 转换数据类型
                image_data = image_data.astype(np.float64)
                
                self.logger.info(f"图像加载成功: {image_data.shape}, 数据类型: {image_data.dtype}")
                self.logger.info(f"数据范围: [{np.min(image_data):.6f}, {np.max(image_data):.6f}]")
                
                return image_data, header, True
                
        except Exception as e:
            self.logger.error(f"加载FITS文件时出错 {fits_path}: {str(e)}")
            return None, None, False
    
    def estimate_background_statistics(self, image_data):
        """
        估计图像背景统计信息
        
        Args:
            image_data (np.ndarray): 输入图像数据
            
        Returns:
            dict: 背景统计信息
        """
        try:
            self.logger.info("估计背景统计信息...")
            
            # 使用SEP进行背景估计
            bkg = sep.Background(image_data)
            
            # 计算sigma-clipped统计
            mean, median, std = sigma_clipped_stats(image_data, sigma=3.0)
            
            # 计算MAD标准差
            mad_std_val = mad_std(image_data)
            
            stats = {
                'sep_background': float(bkg.globalback),
                'sep_rms': float(bkg.globalrms),
                'mean': float(mean),
                'median': float(median),
                'std': float(std),
                'mad_std': float(mad_std_val),
                'background_map': bkg.back()
            }
            
            self.logger.info(f"背景统计: mean={stats['mean']:.6f}, "
                           f"median={stats['median']:.6f}, std={stats['std']:.6f}")
            self.logger.info(f"SEP背景: {stats['sep_background']:.6f}, "
                           f"RMS: {stats['sep_rms']:.6f}")
            
            return stats
            
        except Exception as e:
            self.logger.error(f"估计背景统计时出错: {str(e)}")
            return None
    
    def detect_astronomical_sources(self, image_data, background_stats):
        """
        检测天文源以进行保护
        
        Args:
            image_data (np.ndarray): 输入图像数据
            background_stats (dict): 背景统计信息
            
        Returns:
            list: 检测到的源列表 [(x, y, radius), ...]
        """
        try:
            self.logger.info("检测天文源...")
            
            # 从图像中减去背景
            image_sub = image_data - background_stats['background_map']
            
            # 使用SEP检测源
            threshold = background_stats['sep_rms'] * self.config['source_min_snr']
            
            objects = sep.extract(
                image_sub,
                thresh=threshold,
                err=background_stats['sep_rms'],
                minarea=self.config['source_min_area']
            )
            
            # 转换为保护区域列表
            sources = []
            for obj in objects:
                x = float(obj['x'])
                y = float(obj['y'])
                # 使用配置的保护半径
                radius = self.config['source_protection_radius']
                sources.append((x, y, radius))
            
            self.logger.info(f"检测到 {len(sources)} 个天文源")
            
            return sources
            
        except Exception as e:
            self.logger.error(f"检测天文源时出错: {str(e)}")
            return []
    
    def detect_noise(self, image_data, background_stats):
        """
        检测孤立的单像素噪点

        Args:
            image_data (np.ndarray): 输入图像数据
            background_stats (dict): 背景统计信息

        Returns:
            np.ndarray: 噪声掩码 (True表示噪声)
        """
        try:
            self.logger.info("检测孤立的单像素噪点...")

            # 计算标准化图像
            normalized = (image_data - background_stats['median']) / background_stats['std']

            # 检测异常高值和异常低值
            potential_noise = np.abs(normalized) > self.config['noise_sigma_threshold']

            # 创建结构元素用于检查邻域
            # 使用3x3的十字形结构元素（不包括对角线）
            cross_kernel = np.array([[0, 1, 0],
                                   [1, 1, 1],
                                   [0, 1, 0]], dtype=bool)

            # 也可以使用3x3的方形结构元素（包括对角线）
            square_kernel = np.ones((3, 3), dtype=bool)

            # 使用配置选择结构元素
            if self.config.get('use_cross_kernel', True):
                kernel = cross_kernel
                kernel_name = "十字形"
            else:
                kernel = square_kernel
                kernel_name = "方形"

            self.logger.info(f"使用{kernel_name}邻域检查孤立像素")

            # 检测孤立像素
            noise_mask = np.zeros_like(image_data, dtype=bool)
            isolated_count = 0

            # 获取所有潜在噪声像素的坐标
            noise_coords = np.where(potential_noise)

            for i in range(len(noise_coords[0])):
                y, x = noise_coords[0][i], noise_coords[1][i]

                # 检查边界
                if (y < 1 or y >= image_data.shape[0] - 1 or
                    x < 1 or x >= image_data.shape[1] - 1):
                    continue

                # 提取3x3邻域
                neighborhood = potential_noise[y-1:y+2, x-1:x+2]

                # 检查是否为孤立像素（只有中心像素为True，其他邻域像素为False）
                masked_neighborhood = neighborhood & kernel

                # 如果只有中心像素为True，则认为是孤立噪声
                if np.sum(masked_neighborhood) == 1:  # 只有中心像素
                    noise_mask[y, x] = True
                    isolated_count += 1

            self.logger.info(f"检测到 {isolated_count} 个孤立单像素噪点")

            # 可选：进一步验证孤立噪声的强度特征
            if self.config.get('verify_noise_intensity', True) and isolated_count > 0:
                verified_count = self._verify_isolated_noise(image_data, noise_mask, background_stats)
                self.logger.info(f"经强度验证后保留 {verified_count} 个孤立噪点")

            return noise_mask

        except Exception as e:
            self.logger.error(f"检测噪声时出错: {str(e)}")
            return np.zeros_like(image_data, dtype=bool)

    def _verify_isolated_noise(self, image_data, noise_mask, background_stats):
        """
        验证孤立噪声的强度特征

        Args:
            image_data (np.ndarray): 输入图像数据
            noise_mask (np.ndarray): 初始噪声掩码
            background_stats (dict): 背景统计信息

        Returns:
            int: 验证后的噪声像素数量
        """
        try:
            verified_mask = noise_mask.copy()
            noise_coords = np.where(noise_mask)
            removed_count = 0

            for i in range(len(noise_coords[0])):
                y, x = noise_coords[0][i], noise_coords[1][i]

                # 检查边界
                if (y < 1 or y >= image_data.shape[0] - 1 or
                    x < 1 or x >= image_data.shape[1] - 1):
                    continue

                # 获取像素值和邻域
                pixel_value = image_data[y, x]
                neighborhood = image_data[y-1:y+2, x-1:x+2]

                # 计算邻域统计（排除中心像素）
                neighbor_values = neighborhood.flatten()
                neighbor_values = np.delete(neighbor_values, 4)  # 删除中心像素
                neighbor_mean = np.mean(neighbor_values)
                neighbor_std = np.std(neighbor_values)

                # 检查像素是否真的异常
                if neighbor_std > 0:
                    z_score = abs(pixel_value - neighbor_mean) / neighbor_std

                    # 如果相对于邻域不够异常，则不认为是噪声
                    if z_score < self.config.get('local_noise_threshold', 3.0):
                        verified_mask[y, x] = False
                        removed_count += 1

            verified_count = np.sum(verified_mask)
            self.logger.info(f"强度验证移除了 {removed_count} 个疑似噪声像素")

            # 更新原始掩码
            noise_mask[:] = verified_mask

            return verified_count

        except Exception as e:
            self.logger.error(f"验证噪声强度时出错: {str(e)}")
            return np.sum(noise_mask)

    def detect_satellite_trails(self, image_data, background_stats):
        """
        使用ASTA方法检测卫星拖线（U-Net + 概率霍夫变换）

        Args:
            image_data (np.ndarray): 输入图像数据
            background_stats (dict): 背景统计信息

        Returns:
            np.ndarray: 卫星拖线掩码 (True表示拖线)
        """
        try:
            self.logger.info("使用ASTA方法检测卫星拖线...")

            # 检查是否启用ASTA方法
            if self.config.get('use_asta_method', True):
                return self._detect_trails_asta_method(image_data, background_stats)
            else:
                # 回退到传统方法
                return self._detect_trails_traditional_method(image_data, background_stats)

        except Exception as e:
            self.logger.error(f"检测卫星拖线时出错: {str(e)}")
            return np.zeros_like(image_data, dtype=bool)

    def _detect_trails_asta_method(self, image_data, background_stats):
        """
        ASTA方法：真实的U-Net模型 + 概率霍夫变换

        Args:
            image_data (np.ndarray): 输入图像数据
            background_stats (dict): 背景统计信息

        Returns:
            np.ndarray: 卫星拖线掩码
        """
        try:
            # 检查是否使用真实的ASTA模型
            if (self.config.get('asta_use_real_model', False) and
                self.config.get('asta_model_path') and
                TENSORFLOW_AVAILABLE):
                return self._detect_trails_real_asta_model(image_data, background_stats)
            else:
                return self._detect_trails_simulated_asta(image_data, background_stats)

        except Exception as e:
            self.logger.error(f"ASTA方法检测时出错: {str(e)}")
            return np.zeros_like(image_data, dtype=bool)

    def _detect_trails_real_asta_model(self, image_data, background_stats):
        """
        使用真实的ASTA预训练模型进行检测

        Args:
            image_data (np.ndarray): 输入图像数据
            background_stats (dict): 背景统计信息

        Returns:
            np.ndarray: 卫星拖线掩码
        """
        try:
            self.logger.info("使用真实的ASTA预训练模型...")

            # 加载预训练模型
            model = self._load_asta_model()
            if model is None:
                self.logger.warning("无法加载ASTA模型，回退到模拟方法")
                return self._detect_trails_simulated_asta(image_data, background_stats)

            # 步骤1: 图像预处理和分块
            patches, patch_positions = self._prepare_patches_for_real_asta(image_data, background_stats)

            # 步骤2: 使用真实的U-Net模型进行预测
            unet_predictions = self._predict_with_real_unet(model, patches)

            # 步骤3: 重组预测结果
            combined_prediction = self._combine_patch_predictions(
                unet_predictions, patch_positions, image_data.shape
            )

            # 步骤4: 概率霍夫变换精化
            refined_mask = self._probabilistic_hough_refinement(combined_prediction)

            # 步骤5: 轮廓分析和特征提取
            final_mask = self._contour_analysis_and_filtering(refined_mask, image_data)

            trail_count = np.sum(final_mask)
            self.logger.info(f"真实ASTA模型检测到卫星拖线像素数: {trail_count}")

            return final_mask

        except Exception as e:
            self.logger.error(f"真实ASTA模型检测时出错: {str(e)}")
            self.logger.info("回退到模拟ASTA方法")
            return self._detect_trails_simulated_asta(image_data, background_stats)

    def _detect_trails_simulated_asta(self, image_data, background_stats):
        """
        使用模拟的ASTA方法（基于传统图像处理）

        Args:
            image_data (np.ndarray): 输入图像数据
            background_stats (dict): 背景统计信息

        Returns:
            np.ndarray: 卫星拖线掩码
        """
        try:
            self.logger.info("使用模拟的ASTA方法...")

            # 步骤1: 图像预处理和分块
            patches, patch_positions = self._prepare_patches_for_unet(image_data, background_stats)

            # 步骤2: 使用模拟的U-Net风格检测
            unet_predictions = self._unet_style_detection(patches)

            # 步骤3: 重组预测结果
            combined_prediction = self._combine_patch_predictions(
                unet_predictions, patch_positions, image_data.shape
            )

            # 步骤4: 概率霍夫变换精化
            refined_mask = self._probabilistic_hough_refinement(combined_prediction)

            # 步骤5: 轮廓分析和特征提取
            final_mask = self._contour_analysis_and_filtering(refined_mask, image_data)

            trail_count = np.sum(final_mask)
            self.logger.info(f"模拟ASTA方法检测到卫星拖线像素数: {trail_count}")

            return final_mask

        except Exception as e:
            self.logger.error(f"模拟ASTA方法检测时出错: {str(e)}")
            return np.zeros_like(image_data, dtype=bool)

    def _load_asta_model(self):
        """
        加载ASTA预训练模型

        Returns:
            keras.Model or None: 加载的模型或None
        """
        try:
            model_path = self.config.get('asta_model_path')
            if not model_path or not os.path.exists(model_path):
                self.logger.error(f"ASTA模型文件不存在: {model_path}")
                return None

            if not TENSORFLOW_AVAILABLE:
                self.logger.error("TensorFlow未安装，无法加载ASTA模型")
                return None

            self.logger.info(f"加载ASTA模型: {model_path}")

            # 尝试不同的加载方法来解决兼容性问题
            try:
                # 方法1: 标准加载
                model = keras.models.load_model(model_path, compile=False)
            except Exception as e1:
                self.logger.warning(f"标准加载失败: {str(e1)}")
                try:
                    # 方法2: 使用自定义对象
                    custom_objects = {
                        'LeakyReLU': keras.layers.LeakyReLU,
                        'leaky_relu': keras.layers.LeakyReLU
                    }
                    model = keras.models.load_model(model_path, compile=False, custom_objects=custom_objects)
                except Exception as e2:
                    self.logger.warning(f"自定义对象加载失败: {str(e2)}")
                    try:
                        # 方法3: 使用tf.keras
                        import tensorflow as tf
                        model = tf.keras.models.load_model(model_path, compile=False)
                    except Exception as e3:
                        self.logger.error(f"所有加载方法都失败: {str(e3)}")
                        return None

            self.logger.info(f"ASTA模型加载成功，输入形状: {model.input_shape}")
            self.logger.info(f"ASTA模型输出形状: {model.output_shape}")

            return model

        except Exception as e:
            self.logger.error(f"加载ASTA模型时出错: {str(e)}")
            return None

    def _prepare_patches_for_real_asta(self, image_data, background_stats):
        """
        为真实ASTA模型准备图像块（按照ASTA论文的标准）

        Args:
            image_data (np.ndarray): 输入图像数据
            background_stats (dict): 背景统计信息

        Returns:
            tuple: (图像块列表, 位置信息)
        """
        try:
            patch_size = self.config.get('asta_patch_size', 528)
            overlap = self.config.get('asta_patch_overlap', 64)

            patches = []
            patch_positions = []

            h, w = image_data.shape
            step = patch_size - overlap

            # ASTA标准化：减去背景并标准化
            image_sub = image_data - background_stats['background_map']

            # 使用robust统计进行标准化
            median = background_stats['median']
            mad_std_val = background_stats['mad_std']

            # 标准化到合适的范围
            normalized_image = np.clip((image_sub - median) / (3 * mad_std_val), -3, 3)

            # 转换到[0, 1]范围（ASTA模型期望的输入）
            normalized_image = (normalized_image + 3) / 6

            for y in range(0, h - patch_size + 1, step):
                for x in range(0, w - patch_size + 1, step):
                    # 提取图像块
                    patch = normalized_image[y:y+patch_size, x:x+patch_size]

                    # 确保patch是正确的形状
                    if patch.shape == (patch_size, patch_size):
                        # 添加通道维度（ASTA模型期望单通道输入）
                        patch = np.expand_dims(patch, axis=-1)
                        patches.append(patch)
                        patch_positions.append((y, x))

            # 处理边界区域
            if h % step != 0:
                y = h - patch_size
                for x in range(0, w - patch_size + 1, step):
                    patch = normalized_image[y:y+patch_size, x:x+patch_size]
                    if patch.shape == (patch_size, patch_size):
                        patch = np.expand_dims(patch, axis=-1)
                        patches.append(patch)
                        patch_positions.append((y, x))

            if w % step != 0:
                x = w - patch_size
                for y in range(0, h - patch_size + 1, step):
                    patch = normalized_image[y:y+patch_size, x:x+patch_size]
                    if patch.shape == (patch_size, patch_size):
                        patch = np.expand_dims(patch, axis=-1)
                        patches.append(patch)
                        patch_positions.append((y, x))

            self.logger.info(f"为真实ASTA模型创建了 {len(patches)} 个 {patch_size}x{patch_size} 的图像块")

            return patches, patch_positions

        except Exception as e:
            self.logger.error(f"为真实ASTA模型准备图像块时出错: {str(e)}")
            return [], []

    def _predict_with_real_unet(self, model, patches):
        """
        使用真实的U-Net模型进行预测

        Args:
            model: 加载的Keras模型
            patches (list): 图像块列表

        Returns:
            list: 预测结果列表
        """
        try:
            if not patches:
                return []

            # 转换为numpy数组
            patches_array = np.array(patches)

            self.logger.info(f"使用真实U-Net模型预测 {len(patches)} 个图像块")
            self.logger.info(f"输入形状: {patches_array.shape}")

            # 批量预测
            batch_size = self.config.get('asta_batch_size', 8)  # 避免内存不足
            predictions = []

            for i in range(0, len(patches_array), batch_size):
                batch = patches_array[i:i+batch_size]
                batch_predictions = model.predict(batch, verbose=0)

                # 如果输出有多个通道，取第一个通道
                if len(batch_predictions.shape) == 4 and batch_predictions.shape[-1] > 1:
                    batch_predictions = batch_predictions[..., 0]

                # 移除批次和通道维度
                for j in range(batch_predictions.shape[0]):
                    pred = batch_predictions[j]
                    if len(pred.shape) == 3:
                        pred = pred[..., 0]  # 移除通道维度
                    predictions.append(pred)

            self.logger.info(f"完成 {len(predictions)} 个图像块的真实U-Net预测")

            return predictions

        except Exception as e:
            self.logger.error(f"使用真实U-Net模型预测时出错: {str(e)}")
            return []

    def _prepare_patches_for_unet(self, image_data, background_stats):
        """
        为U-Net风格检测准备图像块

        Args:
            image_data (np.ndarray): 输入图像数据
            background_stats (dict): 背景统计信息

        Returns:
            tuple: (图像块列表, 位置信息)
        """
        try:
            patch_size = self.config.get('asta_patch_size', 528)  # ASTA论文中使用528x528
            overlap = self.config.get('asta_patch_overlap', 64)   # 重叠区域

            patches = []
            patch_positions = []

            h, w = image_data.shape
            step = patch_size - overlap

            # 标准化图像
            normalized_image = self._normalize_for_detection(image_data, background_stats)

            for y in range(0, h - patch_size + 1, step):
                for x in range(0, w - patch_size + 1, step):
                    # 提取图像块
                    patch = normalized_image[y:y+patch_size, x:x+patch_size]
                    patches.append(patch)
                    patch_positions.append((y, x))

            # 处理边界区域
            if h % step != 0:
                y = h - patch_size
                for x in range(0, w - patch_size + 1, step):
                    patch = normalized_image[y:y+patch_size, x:x+patch_size]
                    patches.append(patch)
                    patch_positions.append((y, x))

            if w % step != 0:
                x = w - patch_size
                for y in range(0, h - patch_size + 1, step):
                    patch = normalized_image[y:y+patch_size, x:x+patch_size]
                    patches.append(patch)
                    patch_positions.append((y, x))

            self.logger.info(f"创建了 {len(patches)} 个 {patch_size}x{patch_size} 的图像块")

            return patches, patch_positions

        except Exception as e:
            self.logger.error(f"准备图像块时出错: {str(e)}")
            return [], []

    def _normalize_for_detection(self, image_data, background_stats):
        """
        为检测标准化图像

        Args:
            image_data (np.ndarray): 输入图像数据
            background_stats (dict): 背景统计信息

        Returns:
            np.ndarray: 标准化后的图像
        """
        try:
            # 减去背景
            image_sub = image_data - background_stats['background_map']

            # 使用robust统计进行标准化
            median = background_stats['median']
            mad_std_val = background_stats['mad_std']

            # 标准化到[-1, 1]范围，然后映射到[0, 1]
            normalized = np.clip((image_sub - median) / (3 * mad_std_val), -1, 1)
            normalized = (normalized + 1) / 2  # 映射到[0, 1]

            return normalized.astype(np.float32)

        except Exception as e:
            self.logger.error(f"标准化图像时出错: {str(e)}")
            return image_data.astype(np.float32)

    def _unet_style_detection(self, patches):
        """
        U-Net风格的卫星拖线检测（使用传统图像处理模拟）

        Args:
            patches (list): 图像块列表

        Returns:
            list: 预测掩码列表
        """
        try:
            predictions = []

            for i, patch in enumerate(patches):
                # 模拟U-Net的多尺度特征提取
                prediction = self._simulate_unet_prediction(patch)
                predictions.append(prediction)

            self.logger.info(f"完成 {len(patches)} 个图像块的U-Net风格检测")

            return predictions

        except Exception as e:
            self.logger.error(f"U-Net风格检测时出错: {str(e)}")
            return []

    def _simulate_unet_prediction(self, patch):
        """
        模拟U-Net预测（使用传统图像处理技术）

        Args:
            patch (np.ndarray): 输入图像块

        Returns:
            np.ndarray: 预测概率图 (0-1范围)
        """
        try:
            # 转换为8位图像
            patch_8bit = (patch * 255).astype(np.uint8)

            # 多尺度线检测（模拟U-Net的编码器-解码器结构）

            # 尺度1: 细节检测
            kernel_h1 = cv2.getStructuringElement(cv2.MORPH_RECT, (15, 1))
            kernel_v1 = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 15))
            kernel_d1 = np.eye(7, dtype=np.uint8)
            kernel_d2 = np.fliplr(np.eye(7, dtype=np.uint8))

            lines_h1 = cv2.morphologyEx(patch_8bit, cv2.MORPH_OPEN, kernel_h1)
            lines_v1 = cv2.morphologyEx(patch_8bit, cv2.MORPH_OPEN, kernel_v1)
            lines_d1 = cv2.morphologyEx(patch_8bit, cv2.MORPH_OPEN, kernel_d1)
            lines_d2 = cv2.morphologyEx(patch_8bit, cv2.MORPH_OPEN, kernel_d2)

            # 尺度2: 中等尺度检测
            kernel_h2 = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 1))
            kernel_v2 = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 25))

            lines_h2 = cv2.morphologyEx(patch_8bit, cv2.MORPH_OPEN, kernel_h2)
            lines_v2 = cv2.morphologyEx(patch_8bit, cv2.MORPH_OPEN, kernel_v2)

            # 尺度3: 大尺度检测
            kernel_h3 = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
            kernel_v3 = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 40))

            lines_h3 = cv2.morphologyEx(patch_8bit, cv2.MORPH_OPEN, kernel_h3)
            lines_v3 = cv2.morphologyEx(patch_8bit, cv2.MORPH_OPEN, kernel_v3)

            # 合并多尺度检测结果
            combined = np.maximum.reduce([
                lines_h1, lines_v1, lines_d1, lines_d2,
                lines_h2, lines_v2,
                lines_h3, lines_v3
            ])

            # 应用高斯滤波模拟U-Net的平滑效果
            combined_smooth = gaussian_filter(combined.astype(np.float32), sigma=1.0)

            # 标准化到[0, 1]范围
            if np.max(combined_smooth) > 0:
                prediction = combined_smooth / np.max(combined_smooth)
            else:
                prediction = combined_smooth

            # 应用阈值和形态学操作进一步精化
            prediction = self._refine_unet_prediction(prediction)

            return prediction

        except Exception as e:
            self.logger.error(f"模拟U-Net预测时出错: {str(e)}")
            return np.zeros_like(patch)

    def _refine_unet_prediction(self, prediction):
        """
        精化U-Net预测结果

        Args:
            prediction (np.ndarray): 原始预测

        Returns:
            np.ndarray: 精化后的预测
        """
        try:
            # 应用自适应阈值
            threshold = self.config.get('asta_unet_threshold', 0.3)
            binary_pred = (prediction > threshold).astype(np.uint8)

            # 连通组件分析
            labeled_image = measure.label(binary_pred)
            regions = measure.regionprops(labeled_image)

            # 过滤区域
            refined_mask = np.zeros_like(binary_pred, dtype=bool)

            for region in regions:
                # 检查区域特征
                area = region.area
                eccentricity = region.eccentricity
                major_axis = region.major_axis_length
                minor_axis = region.minor_axis_length

                # 过滤条件（类似ASTA论文中的特征）
                if (area >= 10 and  # 最小面积
                    eccentricity >= 0.7 and  # 高偏心率
                    major_axis >= 20 and  # 最小长度
                    major_axis / max(minor_axis, 1) >= 3):  # 长宽比

                    coords = region.coords
                    refined_mask[coords[:, 0], coords[:, 1]] = True

            # 转换回概率图
            refined_prediction = refined_mask.astype(np.float32)

            # 轻微高斯平滑
            refined_prediction = gaussian_filter(refined_prediction, sigma=0.5)

            return refined_prediction

        except Exception as e:
            self.logger.error(f"精化U-Net预测时出错: {str(e)}")
            return prediction

    def _combine_patch_predictions(self, predictions, patch_positions, image_shape):
        """
        合并图像块预测结果

        Args:
            predictions (list): 预测结果列表
            patch_positions (list): 图像块位置列表
            image_shape (tuple): 原始图像形状

        Returns:
            np.ndarray: 合并后的预测图
        """
        try:
            patch_size = self.config.get('asta_patch_size', 528)
            combined_prediction = np.zeros(image_shape, dtype=np.float32)
            weight_map = np.zeros(image_shape, dtype=np.float32)

            for prediction, (y, x) in zip(predictions, patch_positions):
                # 创建权重图（中心权重高，边缘权重低）
                weight = self._create_patch_weight(patch_size)

                # 确保不超出边界
                end_y = min(y + patch_size, image_shape[0])
                end_x = min(x + patch_size, image_shape[1])
                pred_h = end_y - y
                pred_w = end_x - x

                # 累加预测和权重
                combined_prediction[y:end_y, x:end_x] += prediction[:pred_h, :pred_w] * weight[:pred_h, :pred_w]
                weight_map[y:end_y, x:end_x] += weight[:pred_h, :pred_w]

            # 标准化
            mask = weight_map > 0
            combined_prediction[mask] /= weight_map[mask]

            self.logger.info("完成图像块预测结果合并")

            return combined_prediction

        except Exception as e:
            self.logger.error(f"合并预测结果时出错: {str(e)}")
            return np.zeros(image_shape, dtype=np.float32)

    def _create_patch_weight(self, patch_size):
        """
        创建图像块权重图（中心高，边缘低）

        Args:
            patch_size (int): 图像块大小

        Returns:
            np.ndarray: 权重图
        """
        try:
            # 创建高斯权重
            center = patch_size // 2
            y, x = np.ogrid[:patch_size, :patch_size]

            # 高斯权重函数
            sigma = patch_size / 6  # 调整sigma以控制权重分布
            weight = np.exp(-((x - center)**2 + (y - center)**2) / (2 * sigma**2))

            return weight

        except Exception as e:
            self.logger.error(f"创建权重图时出错: {str(e)}")
            return np.ones((patch_size, patch_size), dtype=np.float32)

    def _probabilistic_hough_refinement(self, prediction_map):
        """
        概率霍夫变换精化（ASTA方法的核心）

        Args:
            prediction_map (np.ndarray): U-Net预测图

        Returns:
            np.ndarray: 精化后的二值掩码
        """
        try:
            # 转换为二值图像
            threshold = self.config.get('asta_hough_threshold', 0.5)
            binary_map = (prediction_map > threshold).astype(np.uint8) * 255

            # 边缘检测
            edges = cv2.Canny(binary_map, 50, 150, apertureSize=3)

            # 概率霍夫变换参数（基于ASTA论文）
            rho = 1  # 距离分辨率
            theta = np.pi / 180  # 角度分辨率
            hough_threshold = self.config.get('asta_hough_votes', 50)
            min_line_length = self.config.get('line_min_length', 100)
            max_line_gap = self.config.get('line_max_gap', 10)

            # 概率霍夫变换
            lines = cv2.HoughLinesP(
                edges,
                rho=rho,
                theta=theta,
                threshold=hough_threshold,
                minLineLength=min_line_length,
                maxLineGap=max_line_gap
            )

            # 创建精化后的掩码
            refined_mask = np.zeros_like(binary_map, dtype=bool)

            if lines is not None:
                for line in lines:
                    x1, y1, x2, y2 = line[0]

                    # 验证线段是否符合卫星拖线特征
                    if self._validate_asta_line(x1, y1, x2, y2, prediction_map):
                        # 在掩码上绘制线段
                        cv2.line(refined_mask.astype(np.uint8), (x1, y1), (x2, y2), 1, thickness=2)

                self.logger.info(f"概率霍夫变换检测到 {len(lines)} 条线段")

            # 与原始预测结合
            combined_mask = np.logical_or(refined_mask, binary_map > 0)

            return combined_mask.astype(bool)

        except Exception as e:
            self.logger.error(f"概率霍夫变换精化时出错: {str(e)}")
            return (prediction_map > 0.5).astype(bool)

    def _validate_asta_line(self, x1, y1, x2, y2, prediction_map):
        """
        验证ASTA检测的线段是否为有效卫星拖线

        Args:
            x1, y1, x2, y2 (int): 线段端点
            prediction_map (np.ndarray): 预测概率图

        Returns:
            bool: 是否为有效拖线
        """
        try:
            # 计算线段长度
            length = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)

            # 基本长度检查
            if length < self.config.get('line_min_length', 100):
                return False

            # 检查线段沿线的预测强度
            num_points = int(length)
            if num_points < 2:
                return False

            # 沿线段采样点
            x_points = np.linspace(x1, x2, num_points).astype(int)
            y_points = np.linspace(y1, y2, num_points).astype(int)

            # 确保点在图像范围内
            valid_mask = ((x_points >= 0) & (x_points < prediction_map.shape[1]) &
                         (y_points >= 0) & (y_points < prediction_map.shape[0]))

            if np.sum(valid_mask) < num_points * 0.8:  # 至少80%的点在图像内
                return False

            # 检查沿线的预测强度
            line_values = prediction_map[y_points[valid_mask], x_points[valid_mask]]
            mean_intensity = np.mean(line_values)

            # 强度阈值
            intensity_threshold = self.config.get('asta_line_intensity_threshold', 0.3)

            return mean_intensity >= intensity_threshold

        except Exception as e:
            self.logger.error(f"验证ASTA线段时出错: {str(e)}")
            return False

    def _contour_analysis_and_filtering(self, refined_mask, original_image):
        """
        轮廓分析和特征提取（ASTA方法的最后步骤）

        Args:
            refined_mask (np.ndarray): 精化后的掩码
            original_image (np.ndarray): 原始图像

        Returns:
            np.ndarray: 最终过滤后的掩码
        """
        try:
            # 连通组件分析
            labeled_mask = measure.label(refined_mask)
            regions = measure.regionprops(labeled_mask, intensity_image=original_image)

            final_mask = np.zeros_like(refined_mask, dtype=bool)
            valid_trails = 0

            for region in regions:
                # 提取特征（基于ASTA论文）
                area = region.area
                eccentricity = region.eccentricity
                major_axis = region.major_axis_length
                minor_axis = region.minor_axis_length
                mean_intensity = region.mean_intensity

                # ASTA验证条件
                conditions = [
                    area >= self.config.get('asta_min_area', 50),
                    major_axis >= self.config.get('line_min_length', 100),
                    eccentricity >= self.config.get('asta_min_eccentricity', 0.8),
                    major_axis / max(minor_axis, 1) >= self.config.get('line_aspect_ratio_min', 8),
                    mean_intensity > np.percentile(original_image, 75)  # 亮度检查
                ]

                if all(conditions):
                    # 检查是否为交叉拖线
                    separated_trails = self._separate_crossing_trails(region)

                    for trail_coords in separated_trails:
                        final_mask[trail_coords[:, 0], trail_coords[:, 1]] = True
                        valid_trails += 1

            self.logger.info(f"轮廓分析后保留 {valid_trails} 条有效卫星拖线")

            return final_mask

        except Exception as e:
            self.logger.error(f"轮廓分析时出错: {str(e)}")
            return refined_mask

    def _separate_crossing_trails(self, region):
        """
        分离交叉的卫星拖线（基于ASTA论文的DBSCAN方法）

        Args:
            region: 区域属性对象

        Returns:
            list: 分离后的拖线坐标列表
        """
        try:
            coords = region.coords

            # 如果区域太小，直接返回
            if len(coords) < 20:
                return [coords]

            # 计算每个像素的局部方向
            directions = []
            for i in range(len(coords)):
                # 使用邻近点计算方向
                neighbors = []
                for j in range(max(0, i-5), min(len(coords), i+6)):
                    if i != j:
                        dy = coords[j][0] - coords[i][0]
                        dx = coords[j][1] - coords[i][1]
                        if dx != 0 or dy != 0:
                            angle = np.arctan2(dy, dx)
                            neighbors.append(angle)

                if neighbors:
                    # 使用中位数角度作为该点的方向
                    median_angle = np.median(neighbors)
                    directions.append(median_angle)
                else:
                    directions.append(0)

            directions = np.array(directions)

            # 使用DBSCAN聚类分离不同方向的拖线
            if not SKLEARN_AVAILABLE:
                # 如果sklearn不可用，直接返回原始坐标
                return [coords]

            # 将角度转换为适合聚类的特征
            angle_features = np.column_stack([np.cos(directions), np.sin(directions)])

            # DBSCAN聚类
            clustering = DBSCAN(eps=0.3, min_samples=10).fit(angle_features)
            labels = clustering.labels_

            # 分离不同的拖线
            separated_trails = []
            unique_labels = set(labels)

            for label in unique_labels:
                if label != -1:  # 忽略噪声点
                    trail_coords = coords[labels == label]
                    if len(trail_coords) >= 20:  # 最小拖线长度
                        separated_trails.append(trail_coords)

            # 如果没有找到有效的聚类，返回原始坐标
            if not separated_trails:
                separated_trails = [coords]

            return separated_trails

        except Exception as e:
            self.logger.error(f"分离交叉拖线时出错: {str(e)}")
            return [region.coords]

    def _detect_trails_traditional_method(self, image_data, background_stats):
        """
        传统方法检测卫星拖线（备选方案）

        Args:
            image_data (np.ndarray): 输入图像数据
            background_stats (dict): 背景统计信息

        Returns:
            np.ndarray: 卫星拖线掩码
        """
        try:
            self.logger.info("使用传统方法检测卫星拖线...")

            # 预处理图像
            processed_image = self._preprocess_for_line_detection(image_data, background_stats)

            # 改进的霍夫变换检测
            hough_lines = self._detect_lines_hough_improved(processed_image)

            # 基于连通组件的细长结构检测
            morphology_lines = self._detect_thin_long_structures(processed_image)

            # 合并并验证检测结果
            trail_mask = self._validate_and_combine_trails(
                processed_image, hough_lines, morphology_lines
            )

            trail_count = np.sum(trail_mask)
            self.logger.info(f"传统方法检测到卫星拖线像素数: {trail_count}")

            return trail_mask

        except Exception as e:
            self.logger.error(f"传统方法检测时出错: {str(e)}")
            return np.zeros_like(image_data, dtype=bool)

    def _preprocess_for_line_detection(self, image_data, background_stats):
        """预处理图像用于线检测"""
        # 减去背景
        image_sub = image_data - background_stats['background_map']

        # 标准化
        normalized = (image_sub - background_stats['median']) / background_stats['std']

        # 阈值化
        threshold = 3.0  # 3-sigma阈值
        binary = normalized > threshold

        # 轻微形态学清理
        kernel = morphology.disk(1)
        cleaned = morphology.opening(binary, kernel)

        return cleaned.astype(np.uint8) * 255

    def _detect_lines_hough_improved(self, binary_image):
        """使用改进的霍夫变换检测细长直线"""
        try:
            # 更精细的边缘检测
            edges = cv2.Canny(binary_image, 30, 100, apertureSize=3)

            # 霍夫直线检测（更严格的参数）
            lines = cv2.HoughLinesP(
                edges,
                rho=1,
                theta=np.pi/180,
                threshold=self.config['line_threshold'],
                minLineLength=self.config['line_min_length'],
                maxLineGap=self.config['line_max_gap']
            )

            valid_lines = []

            if lines is not None:
                for line in lines:
                    x1, y1, x2, y2 = line[0]

                    # 计算线段长度
                    length = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)

                    # 过滤太短的线段
                    if length < self.config['line_min_length']:
                        continue

                    # 检查线段是否足够细长（避免检测到圆形星点）
                    if self._is_valid_satellite_trail(binary_image, x1, y1, x2, y2):
                        valid_lines.append((x1, y1, x2, y2))

                self.logger.info(f"霍夫变换检测到 {len(lines)} 条直线，验证后保留 {len(valid_lines)} 条")

            return valid_lines

        except Exception as e:
            self.logger.error(f"霍夫变换检测直线时出错: {str(e)}")
            return []

    def _is_valid_satellite_trail(self, binary_image, x1, y1, x2, y2):
        """
        验证检测到的线段是否为有效的卫星拖线

        Args:
            binary_image (np.ndarray): 二值图像
            x1, y1, x2, y2 (int): 线段端点坐标

        Returns:
            bool: 是否为有效的卫星拖线
        """
        try:
            # 计算线段长度
            length = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)

            # 创建线段掩码
            line_mask = np.zeros_like(binary_image, dtype=np.uint8)
            cv2.line(line_mask, (x1, y1), (x2, y2), 1, thickness=1)

            # 创建稍宽的线段掩码用于检查宽度
            wide_mask = np.zeros_like(binary_image, dtype=np.uint8)
            cv2.line(wide_mask, (x1, y1), (x2, y2), 1,
                    thickness=self.config['line_max_width'] * 2 + 1)

            # 计算线段区域内的像素数
            line_pixels = np.sum(binary_image[line_mask > 0])
            wide_pixels = np.sum(binary_image[wide_mask > 0])

            if wide_pixels == 0:
                return False

            # 检查线段的"填充度"（线性结构应该有较高的填充度）
            fill_ratio = line_pixels / wide_pixels

            # 检查线段周围的宽度
            actual_width = self._estimate_line_width(binary_image, x1, y1, x2, y2)

            # 验证条件
            conditions = [
                length >= self.config['line_min_length'],  # 足够长
                actual_width <= self.config['line_max_width'],  # 足够细
                fill_ratio > 0.3,  # 有足够的填充
                length / max(actual_width, 1) >= self.config['line_aspect_ratio_min']  # 长宽比
            ]

            return all(conditions)

        except Exception as e:
            self.logger.error(f"验证卫星拖线时出错: {str(e)}")
            return False

    def _estimate_line_width(self, binary_image, x1, y1, x2, y2):
        """
        估计线段的实际宽度

        Args:
            binary_image (np.ndarray): 二值图像
            x1, y1, x2, y2 (int): 线段端点坐标

        Returns:
            float: 估计的线段宽度
        """
        try:
            # 计算线段的方向向量
            dx = x2 - x1
            dy = y2 - y1
            length = np.sqrt(dx**2 + dy**2)

            if length == 0:
                return 0

            # 单位方向向量
            ux = dx / length
            uy = dy / length

            # 垂直方向向量
            vx = -uy
            vy = ux

            # 在线段中点检查垂直方向的宽度
            mid_x = (x1 + x2) // 2
            mid_y = (y1 + y2) // 2

            # 沿垂直方向搜索线段边界
            max_search = min(20, binary_image.shape[0] // 10, binary_image.shape[1] // 10)

            width = 0
            for offset in range(1, max_search):
                # 正方向
                px1 = int(mid_x + offset * vx)
                py1 = int(mid_y + offset * vy)
                # 负方向
                px2 = int(mid_x - offset * vx)
                py2 = int(mid_y - offset * vy)

                # 检查边界
                if (0 <= px1 < binary_image.shape[1] and 0 <= py1 < binary_image.shape[0] and
                    0 <= px2 < binary_image.shape[1] and 0 <= py2 < binary_image.shape[0]):

                    if binary_image[py1, px1] > 0 or binary_image[py2, px2] > 0:
                        width = offset * 2
                    else:
                        break
                else:
                    break

            return max(width, 1)

        except Exception as e:
            self.logger.error(f"估计线段宽度时出错: {str(e)}")
            return 1

    def _detect_thin_long_structures(self, binary_image):
        """使用连通组件分析检测细长结构"""
        try:
            # 连通组件分析
            labeled_image = measure.label(binary_image)
            regions = measure.regionprops(labeled_image)

            thin_long_structures = []

            for region in regions:
                # 基本几何特征
                area = region.area
                bbox = region.bbox  # (min_row, min_col, max_row, max_col)
                height = bbox[2] - bbox[0]
                width = bbox[3] - bbox[1]

                # 跳过太小的区域
                if area < self.config['line_min_length'] // 2:
                    continue

                # 计算长宽比
                aspect_ratio = max(height, width) / max(min(height, width), 1)

                # 计算偏心率（椭圆拟合）
                eccentricity = region.eccentricity

                # 计算填充度（实际面积 / 边界框面积）
                bbox_area = height * width
                solidity = area / max(bbox_area, 1)

                # 计算主轴长度
                major_axis_length = region.major_axis_length
                minor_axis_length = region.minor_axis_length

                # 验证是否为细长结构
                conditions = [
                    major_axis_length >= self.config['line_min_length'],  # 足够长
                    minor_axis_length <= self.config['line_max_width'],   # 足够细
                    aspect_ratio >= self.config['line_aspect_ratio_min'], # 长宽比
                    eccentricity >= self.config['line_straightness_min'], # 直线度
                    solidity > 0.3  # 填充度
                ]

                if all(conditions):
                    thin_long_structures.append(region)

            self.logger.info(f"连通组件分析检测到 {len(thin_long_structures)} 个细长结构")

            return thin_long_structures

        except Exception as e:
            self.logger.error(f"检测细长结构时出错: {str(e)}")
            return []

    def _validate_and_combine_trails(self, binary_image, hough_lines, morphology_regions):
        """
        验证并合并霍夫变换和形态学检测的结果

        Args:
            binary_image (np.ndarray): 二值图像
            hough_lines (list): 霍夫变换检测的线段
            morphology_regions (list): 形态学检测的区域

        Returns:
            np.ndarray: 最终的拖线掩码
        """
        try:
            trail_mask = np.zeros_like(binary_image, dtype=bool)

            # 处理霍夫变换检测的线段
            for x1, y1, x2, y2 in hough_lines:
                # 绘制细线（宽度为1）
                line_mask = np.zeros_like(binary_image, dtype=np.uint8)
                cv2.line(line_mask, (x1, y1), (x2, y2), 1, thickness=1)
                trail_mask |= (line_mask > 0)

            # 处理形态学检测的区域
            for region in morphology_regions:
                coords = region.coords
                trail_mask[coords[:, 0], coords[:, 1]] = True

            # 进一步验证：去除可能的星点
            trail_mask = self._remove_star_like_objects(trail_mask, binary_image)

            # 最小化后处理，保持线条细致
            if np.any(trail_mask):
                # 只进行轻微的连接操作
                kernel = morphology.rectangle(3, 1)  # 水平连接
                trail_mask = morphology.closing(trail_mask, kernel)

                kernel = morphology.rectangle(1, 3)  # 垂直连接
                trail_mask = morphology.closing(trail_mask, kernel)

                # 去除太小的区域
                trail_mask = morphology.remove_small_objects(
                    trail_mask, min_size=self.config['line_min_length'] // 2
                )

            return trail_mask

        except Exception as e:
            self.logger.error(f"验证和合并拖线时出错: {str(e)}")
            return np.zeros_like(binary_image, dtype=bool)

    def _remove_star_like_objects(self, trail_mask, binary_image):
        """
        从拖线掩码中移除类似星点的对象

        Args:
            trail_mask (np.ndarray): 拖线掩码
            binary_image (np.ndarray): 二值图像

        Returns:
            np.ndarray: 清理后的拖线掩码
        """
        try:
            # 连通组件分析
            labeled_mask = measure.label(trail_mask)
            regions = measure.regionprops(labeled_mask)

            cleaned_mask = np.zeros_like(trail_mask, dtype=bool)

            for region in regions:
                # 计算几何特征
                area = region.area
                eccentricity = region.eccentricity
                major_axis = region.major_axis_length
                minor_axis = region.minor_axis_length

                # 计算圆形度（4π*面积/周长²）
                perimeter = region.perimeter
                if perimeter > 0:
                    circularity = 4 * np.pi * area / (perimeter ** 2)
                else:
                    circularity = 0

                # 判断是否为星点（圆形、低偏心率）
                is_star_like = (
                    circularity > 0.5 or  # 太圆
                    eccentricity < 0.7 or  # 偏心率太低
                    major_axis / max(minor_axis, 1) < 3  # 长宽比太小
                )

                # 如果不像星点，保留该区域
                if not is_star_like:
                    coords = region.coords
                    cleaned_mask[coords[:, 0], coords[:, 1]] = True

            removed_count = np.sum(trail_mask) - np.sum(cleaned_mask)
            if removed_count > 0:
                self.logger.info(f"移除了 {removed_count} 个类似星点的像素")

            return cleaned_mask

        except Exception as e:
            self.logger.error(f"移除星点时出错: {str(e)}")
            return trail_mask

    def create_protection_mask(self, image_shape, sources):
        """
        创建天文源保护掩码

        Args:
            image_shape (tuple): 图像形状
            sources (list): 源列表 [(x, y, radius), ...]

        Returns:
            np.ndarray: 保护掩码 (True表示需要保护的区域)
        """
        protection_mask = np.zeros(image_shape, dtype=bool)

        for x, y, radius in sources:
            # 创建圆形保护区域
            yy, xx = np.ogrid[:image_shape[0], :image_shape[1]]
            mask = (xx - x)**2 + (yy - y)**2 <= radius**2
            protection_mask |= mask

        self.logger.info(f"创建了 {len(sources)} 个源的保护掩码")

        return protection_mask

    def process_fits_file(self, fits_path, output_dir=None):
        """
        处理单个FITS文件

        Args:
            fits_path (str): 输入FITS文件路径
            output_dir (str): 输出目录，默认为输入文件所在目录

        Returns:
            dict: 处理结果信息
        """
        try:
            self.logger.info(f"开始处理FITS文件: {fits_path}")

            # 1. 加载图像
            image_data, header, success = self.load_fits_image(fits_path)
            if not success:
                return {'success': False, 'error': '无法加载FITS文件'}

            # 2. 估计背景统计
            background_stats = self.estimate_background_statistics(image_data)
            if background_stats is None:
                return {'success': False, 'error': '无法估计背景统计'}

            # 3. 检测天文源
            sources = self.detect_astronomical_sources(image_data, background_stats)
            protection_mask = self.create_protection_mask(image_data.shape, sources)

            # 4. 检测噪声
            noise_mask = self.detect_noise(image_data, background_stats)

            # 5. 检测卫星拖线
            trail_mask = self.detect_satellite_trails(image_data, background_stats)

            # 6. 合并掩码（排除保护区域）
            combined_mask = np.logical_or(noise_mask, trail_mask)
            combined_mask = np.logical_and(combined_mask, ~protection_mask)

            # 7. 创建清理后的图像
            cleaned_image = self.create_cleaned_image(image_data, combined_mask, background_stats)

            # 8. 保存结果
            result = self.save_results(
                fits_path, cleaned_image, combined_mask, noise_mask, trail_mask,
                protection_mask, header, output_dir
            )

            # 9. 统计信息
            total_pixels = image_data.size
            noise_pixels = np.sum(noise_mask)
            trail_pixels = np.sum(trail_mask)
            protected_pixels = np.sum(protection_mask)
            cleaned_pixels = np.sum(combined_mask)

            result.update({
                'success': True,
                'statistics': {
                    'total_pixels': total_pixels,
                    'noise_pixels': noise_pixels,
                    'trail_pixels': trail_pixels,
                    'protected_pixels': protected_pixels,
                    'cleaned_pixels': cleaned_pixels,
                    'noise_percentage': noise_pixels / total_pixels * 100,
                    'trail_percentage': trail_pixels / total_pixels * 100,
                    'protected_percentage': protected_pixels / total_pixels * 100,
                    'cleaned_percentage': cleaned_pixels / total_pixels * 100,
                    'sources_detected': len(sources)
                }
            })

            self.logger.info("FITS文件处理完成")
            self.logger.info(f"统计信息: 噪声={noise_pixels}像素({noise_pixels/total_pixels*100:.2f}%), "
                           f"拖线={trail_pixels}像素({trail_pixels/total_pixels*100:.2f}%), "
                           f"保护={protected_pixels}像素({protected_pixels/total_pixels*100:.2f}%)")

            return result

        except Exception as e:
            self.logger.error(f"处理FITS文件时出错: {str(e)}")
            return {'success': False, 'error': str(e)}

    def create_cleaned_image(self, image_data, artifact_mask, background_stats):
        """
        创建清理后的图像

        Args:
            image_data (np.ndarray): 原始图像数据
            artifact_mask (np.ndarray): 需要清理的区域掩码
            background_stats (dict): 背景统计信息

        Returns:
            np.ndarray: 清理后的图像
        """
        try:
            cleaned_image = image_data.copy()

            # 方法1: 用局部背景值替换
            if np.any(artifact_mask):
                # 使用背景图替换被掩码的区域
                cleaned_image[artifact_mask] = background_stats['background_map'][artifact_mask]

                # 轻微高斯平滑以减少边界效应
                if self.config['gaussian_sigma'] > 0:
                    # 只对被替换的区域进行平滑
                    smoothed = gaussian_filter(cleaned_image, sigma=self.config['gaussian_sigma'])

                    # 创建平滑过渡区域
                    kernel = morphology.disk(3)
                    dilated_mask = morphology.dilation(artifact_mask, kernel)
                    transition_mask = np.logical_and(dilated_mask, ~artifact_mask)

                    # 在过渡区域应用平滑
                    cleaned_image[transition_mask] = smoothed[transition_mask]

            self.logger.info("创建清理后图像完成")

            return cleaned_image

        except Exception as e:
            self.logger.error(f"创建清理后图像时出错: {str(e)}")
            return image_data

    def save_results(self, input_path, cleaned_image, combined_mask, noise_mask,
                    trail_mask, protection_mask, header, output_dir=None):
        """
        保存处理结果

        Args:
            input_path (str): 输入文件路径
            cleaned_image (np.ndarray): 清理后的图像
            combined_mask (np.ndarray): 合并掩码
            noise_mask (np.ndarray): 噪声掩码
            trail_mask (np.ndarray): 拖线掩码
            protection_mask (np.ndarray): 保护掩码
            header: FITS头信息
            output_dir (str): 输出目录

        Returns:
            dict: 保存的文件信息
        """
        try:
            # 确定输出目录
            if output_dir is None:
                output_dir = os.path.dirname(input_path)

            os.makedirs(output_dir, exist_ok=True)

            # 生成输出文件名
            base_name = os.path.splitext(os.path.basename(input_path))[0]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            saved_files = {}

            # 保存清理后的FITS文件
            if self.config['output_format'] in ['fits', 'both']:
                cleaned_fits_path = os.path.join(output_dir, f"{base_name}_cleaned_{timestamp}.fits")

                # 更新header信息
                new_header = header.copy()
                new_header['HISTORY'] = f'Processed by NoiseSatelliteDetector on {datetime.now()}'
                new_header['HISTORY'] = 'Noise and satellite trails removed'

                # 保存FITS文件
                fits.writeto(cleaned_fits_path, cleaned_image, header=new_header, overwrite=True)
                saved_files['cleaned_fits'] = cleaned_fits_path
                self.logger.info(f"保存清理后FITS文件: {cleaned_fits_path}")

            # 保存掩码文件（如果启用中间结果保存）
            if self.config['save_intermediate']:
                # 合并掩码
                combined_mask_path = os.path.join(output_dir, f"{base_name}_mask_{timestamp}.fits")
                fits.writeto(combined_mask_path, combined_mask.astype(np.uint8), overwrite=True)
                saved_files['combined_mask'] = combined_mask_path

                # 噪声掩码
                noise_mask_path = os.path.join(output_dir, f"{base_name}_noise_mask_{timestamp}.fits")
                fits.writeto(noise_mask_path, noise_mask.astype(np.uint8), overwrite=True)
                saved_files['noise_mask'] = noise_mask_path

                # 拖线掩码
                trail_mask_path = os.path.join(output_dir, f"{base_name}_trail_mask_{timestamp}.fits")
                fits.writeto(trail_mask_path, trail_mask.astype(np.uint8), overwrite=True)
                saved_files['trail_mask'] = trail_mask_path

                self.logger.info("保存中间掩码文件完成")

            # 创建可视化图像
            if self.config['create_visualization']:
                if self.config['output_format'] in ['jpg', 'both']:
                    viz_path = os.path.join(output_dir, f"{base_name}_visualization_{timestamp}.jpg")
                    self.create_visualization(
                        input_path, cleaned_image, combined_mask, noise_mask,
                        trail_mask, protection_mask, viz_path
                    )
                    saved_files['visualization'] = viz_path

            return saved_files

        except Exception as e:
            self.logger.error(f"保存结果时出错: {str(e)}")
            return {}

    def create_visualization(self, input_path, cleaned_image, combined_mask,
                           noise_mask, trail_mask, protection_mask, output_path):
        """
        创建可视化图像

        Args:
            input_path (str): 输入文件路径
            cleaned_image (np.ndarray): 清理后的图像
            combined_mask (np.ndarray): 合并掩码
            noise_mask (np.ndarray): 噪声掩码
            trail_mask (np.ndarray): 拖线掩码
            protection_mask (np.ndarray): 保护掩码
            output_path (str): 输出路径
        """
        try:
            # 加载原始图像用于对比
            original_image, _, _ = self.load_fits_image(input_path)

            # 创建图像显示
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))
            fig.suptitle(f'噪声和卫星拖线检测结果\n{os.path.basename(input_path)}', fontsize=16)

            # 计算显示范围
            vmin, vmax = np.percentile(original_image, [1, 99])

            # 原始图像
            im1 = axes[0, 0].imshow(original_image, cmap='gray', vmin=vmin, vmax=vmax)
            axes[0, 0].set_title('原始图像')
            axes[0, 0].axis('off')

            # 清理后图像
            im2 = axes[0, 1].imshow(cleaned_image, cmap='gray', vmin=vmin, vmax=vmax)
            axes[0, 1].set_title('清理后图像')
            axes[0, 1].axis('off')

            # 差异图像
            diff_image = original_image - cleaned_image
            im3 = axes[0, 2].imshow(diff_image, cmap='RdBu_r', vmin=-np.std(diff_image)*3, vmax=np.std(diff_image)*3)
            axes[0, 2].set_title('差异图像 (原始 - 清理后)')
            axes[0, 2].axis('off')

            # 噪声掩码
            axes[1, 0].imshow(noise_mask, cmap='Reds', alpha=0.7)
            axes[1, 0].imshow(original_image, cmap='gray', alpha=0.3, vmin=vmin, vmax=vmax)
            axes[1, 0].set_title(f'噪声检测 ({np.sum(noise_mask)} 像素)')
            axes[1, 0].axis('off')

            # 拖线掩码
            axes[1, 1].imshow(trail_mask, cmap='Blues', alpha=0.7)
            axes[1, 1].imshow(original_image, cmap='gray', alpha=0.3, vmin=vmin, vmax=vmax)
            axes[1, 1].set_title(f'卫星拖线检测 ({np.sum(trail_mask)} 像素)')
            axes[1, 1].axis('off')

            # 保护区域
            axes[1, 2].imshow(protection_mask, cmap='Greens', alpha=0.7)
            axes[1, 2].imshow(original_image, cmap='gray', alpha=0.3, vmin=vmin, vmax=vmax)
            axes[1, 2].set_title(f'天文源保护区域 ({np.sum(protection_mask)} 像素)')
            axes[1, 2].axis('off')

            # 添加颜色条
            plt.colorbar(im1, ax=axes[0, 0], fraction=0.046, pad=0.04)
            plt.colorbar(im2, ax=axes[0, 1], fraction=0.046, pad=0.04)
            plt.colorbar(im3, ax=axes[0, 2], fraction=0.046, pad=0.04)

            plt.tight_layout()
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()

            self.logger.info(f"保存可视化图像: {output_path}")

        except Exception as e:
            self.logger.error(f"创建可视化图像时出错: {str(e)}")


def main():
    """主函数 - 命令行接口"""
    import argparse

    parser = argparse.ArgumentParser(
        description='FITS文件孤立单像素噪点和卫星拖线检测与去除工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python noise_satellite_detector.py -i image.fits
  python noise_satellite_detector.py -i image.fits -o output_dir --config strict
  python noise_satellite_detector.py -i image.fits --no-viz --fits-only

配置说明:
  default - 默认配置，使用十字形邻域检测孤立单像素噪点
  strict  - 严格模式，更敏感的检测，使用更低的阈值
  gentle  - 温和模式，使用方形邻域，更保守的检测

特点:
  - 专门检测孤立的单像素噪点，不会误删天文源边缘
  - 支持十字形和方形邻域检查模式
  - 局部强度验证确保检测精度
        """
    )

    parser.add_argument('-i', '--input', required=True,
                       help='输入FITS文件路径')
    parser.add_argument('-o', '--output',
                       help='输出目录（默认为输入文件所在目录）')
    parser.add_argument('-c', '--config', choices=['default', 'strict', 'gentle'],
                       default='default',
                       help='检测配置模式（默认: default）')
    parser.add_argument('--no-viz', action='store_true',
                       help='不创建可视化图像')
    parser.add_argument('--no-intermediate', action='store_true',
                       help='不保存中间掩码文件')
    parser.add_argument('--fits-only', action='store_true',
                       help='仅输出FITS格式文件')
    parser.add_argument('--jpg-only', action='store_true',
                       help='仅输出JPG格式文件')

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        return 1

    # 预设配置 - 孤立单像素噪点 + ASTA卫星拖线检测
    configs = {
        'default': {
            'noise_sigma_threshold': 5.0,
            'use_cross_kernel': True,
            'verify_noise_intensity': True,
            'local_noise_threshold': 3.0,
            'use_asta_method': True,
            'asta_use_real_model': False,
            'asta_model_path': None,
            'asta_patch_size': 528,
            'asta_unet_threshold': 0.5,
            'asta_hough_threshold': 0.5,
            'asta_hough_votes': 50,
            'asta_batch_size': 8,
            'line_min_length': 100,
            'line_aspect_ratio_min': 8,
            'source_min_snr': 5.0,
            'source_protection_radius': 10,
            'gaussian_sigma': 0.5
        },
        'strict': {
            'noise_sigma_threshold': 4.0,
            'use_cross_kernel': True,
            'verify_noise_intensity': True,
            'local_noise_threshold': 2.5,
            'use_asta_method': True,
            'asta_use_real_model': False,
            'asta_model_path': None,
            'asta_patch_size': 528,
            'asta_unet_threshold': 0.3,
            'asta_hough_threshold': 0.4,
            'asta_hough_votes': 40,
            'asta_batch_size': 8,
            'line_min_length': 80,
            'line_aspect_ratio_min': 10,
            'source_min_snr': 6.0,
            'source_protection_radius': 8,
            'gaussian_sigma': 0.3
        },
        'gentle': {
            'noise_sigma_threshold': 6.0,
            'use_cross_kernel': False,  # 使用方形邻域，更保守
            'verify_noise_intensity': True,
            'local_noise_threshold': 4.0,
            'use_asta_method': True,
            'asta_use_real_model': False,
            'asta_model_path': None,
            'asta_patch_size': 528,
            'asta_unet_threshold': 0.7,
            'asta_hough_threshold': 0.6,
            'asta_hough_votes': 60,
            'asta_batch_size': 8,
            'line_min_length': 150,
            'line_aspect_ratio_min': 6,
            'source_min_snr': 4.0,
            'source_protection_radius': 12,
            'gaussian_sigma': 0.7
        }
    }

    # 构建配置
    config = configs[args.config].copy()

    # 处理输出格式选项
    if args.fits_only:
        config['output_format'] = 'fits'
    elif args.jpg_only:
        config['output_format'] = 'jpg'
    else:
        config['output_format'] = 'both'

    # 处理可视化选项
    config['create_visualization'] = not args.no_viz
    config['save_intermediate'] = not args.no_intermediate

    print("=" * 60)
    print("FITS文件孤立单像素噪点和卫星拖线检测与去除工具")
    print("=" * 60)
    print(f"输入文件: {args.input}")
    print(f"输出目录: {args.output or '输入文件所在目录'}")
    print(f"检测配置: {args.config}")
    print(f"输出格式: {config['output_format']}")
    print(f"创建可视化: {'是' if config['create_visualization'] else '否'}")
    print(f"保存中间文件: {'是' if config['save_intermediate'] else '否'}")
    print("-" * 60)

    # 创建检测器
    detector = NoiseSatelliteDetector(config=config)

    # 处理文件
    result = detector.process_fits_file(args.input, args.output)

    if result['success']:
        print("\n✓ 处理完成!")

        # 显示统计信息
        stats = result['statistics']
        print(f"\n统计信息:")
        print(f"  总像素数: {stats['total_pixels']:,}")
        print(f"  检测到的源: {stats['sources_detected']}")
        print(f"  噪声像素: {stats['noise_pixels']:,} ({stats['noise_percentage']:.2f}%)")
        print(f"  拖线像素: {stats['trail_pixels']:,} ({stats['trail_percentage']:.2f}%)")
        print(f"  保护像素: {stats['protected_pixels']:,} ({stats['protected_percentage']:.2f}%)")
        print(f"  清理像素: {stats['cleaned_pixels']:,} ({stats['cleaned_percentage']:.2f}%)")

        # 显示输出文件
        print(f"\n输出文件:")
        for file_type, file_path in result.items():
            if file_type != 'success' and file_type != 'statistics':
                print(f"  {file_type}: {file_path}")

        return 0
    else:
        print(f"\n✗ 处理失败: {result.get('error', '未知错误')}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
